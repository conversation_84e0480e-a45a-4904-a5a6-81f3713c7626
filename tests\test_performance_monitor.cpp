#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTimer>
#include <QElapsedTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QApplication>

#include "PerformanceMonitor.h"

class TestPerformanceMonitor : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic functionality tests
    void testMonitorCreation();
    void testStartStopMonitoring();
    void testMonitoringState();

    // Metric recording tests
    void testRecordMetric();
    void testRecordRenderTime();
    void testRecordCacheHit();
    void testRecordCacheMiss();
    void testRecordMemoryUsage();
    void testRecordActiveThreads();

    // Performance timer tests
    void testStartEndTimer();
    void testMultipleTimers();
    void testTimerWithoutStart();
    void testTimerOverwrite();

    // Data access tests
    void testGetMetrics();
    void testGetMetricsByCategory();
    void testGetRecentMetrics();
    void testGetRenderingStats();
    void testGetSystemMetrics();

    // Statistics tests
    void testGetAverageMetric();
    void testGetPeakMetric();
    void testGetCategorySummary();

    // Optimization recommendation tests
    void testOptimizationRecommendations();
    void testShouldOptimizeCache();
    void testShouldReduceThreads();
    void testShouldClearMemory();

    // Configuration tests
    void testSetMonitoringInterval();
    void testSetMaxHistorySize();
    void testSetMetricRetentionTime();

    // Export/Import tests
    void testExportMetricsToJson();
    void testExportMetricsToCsv();
    void testImportMetricsFromJson();
    void testExportImportRoundTrip();

    // Signal emission tests
    void testMetricsUpdatedSignal();
    void testPerformanceAlertSignal();
    void testOptimizationRecommendedSignal();
    void testMemoryThresholdExceededSignal();
    void testRenderingPerformanceChangedSignal();

    // System metrics tests
    void testSystemMetricsCollection();
    void testSystemMetricsHistory();
    void testPerformanceThresholds();

    // Thread safety tests
    void testConcurrentMetricRecording();
    void testConcurrentTimerOperations();

    // Edge cases and validation
    void testNegativeValues();
    void testExtremeValues();
    void testEmptyMetrics();
    void testInvalidTimerOperations();

    // Integration tests
    void testCompleteMonitoringWorkflow();
    void testPerformanceAnalysis();
    void testMetricCleanup();

private:
    PerformanceMonitor* m_monitor;
    
    void waitForSignal(QObject* sender, const char* signal, int timeoutMs = 1000);
    void recordSampleMetrics();
    void verifyMetricExists(const QString& name, const QString& category);
    double calculateExpectedAverage(const QList<double>& values);
};

void TestPerformanceMonitor::initTestCase()
{
    // Initialize application if not already done
    if (!QApplication::instance()) {
        int argc = 0;
        char** argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestPerformanceMonitor::cleanupTestCase()
{
    // Cleanup handled by Qt
}

void TestPerformanceMonitor::init()
{
    // Create fresh performance monitor for each test
    m_monitor = new PerformanceMonitor(this);
}

void TestPerformanceMonitor::cleanup()
{
    if (m_monitor) {
        m_monitor->stopMonitoring();
        delete m_monitor;
        m_monitor = nullptr;
    }
}

void TestPerformanceMonitor::testMonitorCreation()
{
    // Test that monitor is created successfully
    QVERIFY(m_monitor != nullptr);
    
    // Test initial state
    QVERIFY(!m_monitor->isMonitoring());
}

void TestPerformanceMonitor::testStartStopMonitoring()
{
    // Test starting monitoring
    m_monitor->startMonitoring();
    QVERIFY(m_monitor->isMonitoring());
    
    // Test stopping monitoring
    m_monitor->stopMonitoring();
    QVERIFY(!m_monitor->isMonitoring());
    
    // Test multiple start/stop cycles
    for (int i = 0; i < 3; ++i) {
        m_monitor->startMonitoring();
        QVERIFY(m_monitor->isMonitoring());
        
        m_monitor->stopMonitoring();
        QVERIFY(!m_monitor->isMonitoring());
    }
}

void TestPerformanceMonitor::testMonitoringState()
{
    // Test initial state
    QVERIFY(!m_monitor->isMonitoring());
    
    // Test state after starting
    m_monitor->startMonitoring();
    QVERIFY(m_monitor->isMonitoring());
    
    // Test idempotent start
    m_monitor->startMonitoring();
    QVERIFY(m_monitor->isMonitoring());
    
    // Test state after stopping
    m_monitor->stopMonitoring();
    QVERIFY(!m_monitor->isMonitoring());
    
    // Test idempotent stop
    m_monitor->stopMonitoring();
    QVERIFY(!m_monitor->isMonitoring());
}

void TestPerformanceMonitor::testRecordMetric()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Record a metric
    m_monitor->recordMetric("TestMetric", "TestCategory", 42.5, "units");
    
    // Verify signal was emitted
    QCOMPARE(spy.count(), 1);
    
    // Verify metric was recorded
    QList<PerformanceMetric> metrics = m_monitor->getMetrics("TestCategory");
    QCOMPARE(metrics.size(), 1);
    
    const PerformanceMetric& metric = metrics.first();
    QCOMPARE(metric.name, QString("TestMetric"));
    QCOMPARE(metric.category, QString("TestCategory"));
    QCOMPARE(metric.value, 42.5);
    QCOMPARE(metric.unit, QString("units"));
    QVERIFY(metric.timestamp.isValid());
}

void TestPerformanceMonitor::testRecordRenderTime()
{
    QSignalSpy renderingSpy(m_monitor, &PerformanceMonitor::renderingPerformanceChanged);
    QSignalSpy metricsSpy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Record render time
    double renderTime = 150.5;
    m_monitor->recordRenderTime(renderTime);
    
    // Verify signals were emitted
    QCOMPARE(metricsSpy.count(), 1);
    QCOMPARE(renderingSpy.count(), 1);
    
    // Verify rendering stats were updated
    RenderingStats stats = m_monitor->getRenderingStats();
    QCOMPARE(stats.totalPagesRendered, 1);
    QCOMPARE(stats.averageRenderTime, renderTime);
    
    // Verify metric was recorded
    verifyMetricExists("RenderTime", "Rendering");
}

void TestPerformanceMonitor::testRecordCacheHit()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Record cache hit
    m_monitor->recordCacheHit();
    
    // Verify signal was emitted
    QCOMPARE(spy.count(), 1);
    
    // Verify rendering stats were updated
    RenderingStats stats = m_monitor->getRenderingStats();
    QCOMPARE(stats.cacheHits, 1);
    QCOMPARE(stats.cacheMisses, 0);
    
    // Verify metric was recorded
    verifyMetricExists("CacheHit", "Cache");
}

void TestPerformanceMonitor::testRecordCacheMiss()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Record cache miss
    m_monitor->recordCacheMiss();
    
    // Verify signal was emitted
    QCOMPARE(spy.count(), 1);
    
    // Verify rendering stats were updated
    RenderingStats stats = m_monitor->getRenderingStats();
    QCOMPARE(stats.cacheHits, 0);
    QCOMPARE(stats.cacheMisses, 1);
    
    // Verify metric was recorded
    verifyMetricExists("CacheMiss", "Cache");
}

void TestPerformanceMonitor::testRecordMemoryUsage()
{
    QSignalSpy metricsSpy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Record memory usage
    qint64 memoryBytes = 100 * 1024 * 1024; // 100 MB
    m_monitor->recordMemoryUsage(memoryBytes);
    
    // Verify signal was emitted
    QCOMPARE(metricsSpy.count(), 1);
    
    // Verify rendering stats were updated
    RenderingStats stats = m_monitor->getRenderingStats();
    QCOMPARE(stats.peakMemoryUsage, 100.0); // 100 MB
    
    // Verify metric was recorded
    verifyMetricExists("MemoryUsage", "Memory");
}

void TestPerformanceMonitor::testRecordActiveThreads()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Record active threads
    int threadCount = 4;
    m_monitor->recordActiveThreads(threadCount);
    
    // Verify signal was emitted
    QCOMPARE(spy.count(), 1);
    
    // Verify rendering stats were updated
    RenderingStats stats = m_monitor->getRenderingStats();
    QCOMPARE(stats.activeThreads, threadCount);
    
    // Verify metric was recorded
    verifyMetricExists("ActiveThreads", "Threading");
}

void TestPerformanceMonitor::testStartEndTimer()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Start timer
    QString operation = "TestOperation";
    m_monitor->startTimer(operation);
    
    // Wait a bit
    QTest::qWait(50);
    
    // End timer
    m_monitor->endTimer(operation);
    
    // Verify signal was emitted
    QCOMPARE(spy.count(), 1);
    
    // Verify metric was recorded
    verifyMetricExists(operation, "Performance");
    
    // Verify timing is reasonable
    QList<PerformanceMetric> metrics = m_monitor->getMetrics("Performance");
    QVERIFY(metrics.size() > 0);
    
    const PerformanceMetric& metric = metrics.last();
    QVERIFY(metric.value >= 40); // At least 40ms (allowing for timing variations)
    QVERIFY(metric.value <= 200); // Less than 200ms (reasonable upper bound)
}

void TestPerformanceMonitor::testMultipleTimers()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);
    
    // Start multiple timers
    m_monitor->startTimer("Operation1");
    m_monitor->startTimer("Operation2");
    m_monitor->startTimer("Operation3");
    
    QTest::qWait(30);
    
    // End timers in different order
    m_monitor->endTimer("Operation2");
    m_monitor->endTimer("Operation1");
    m_monitor->endTimer("Operation3");
    
    // Verify all timers recorded metrics
    QCOMPARE(spy.count(), 3);
    
    verifyMetricExists("Operation1", "Performance");
    verifyMetricExists("Operation2", "Performance");
    verifyMetricExists("Operation3", "Performance");
}

void TestPerformanceMonitor::waitForSignal(QObject* sender, const char* signal, int timeoutMs)
{
    QSignalSpy spy(sender, signal);
    QTimer timeout;
    timeout.setSingleShot(true);
    timeout.start(timeoutMs);
    
    QEventLoop loop;
    connect(sender, signal, &loop, &QEventLoop::quit);
    connect(&timeout, &QTimer::timeout, &loop, &QEventLoop::quit);
    
    loop.exec();
}

void TestPerformanceMonitor::recordSampleMetrics()
{
    m_monitor->recordMetric("CPU", "System", 45.5, "%");
    m_monitor->recordMetric("Memory", "System", 512.0, "MB");
    m_monitor->recordMetric("RenderTime", "Rendering", 125.0, "ms");
    m_monitor->recordCacheHit();
    m_monitor->recordCacheMiss();
}

void TestPerformanceMonitor::verifyMetricExists(const QString& name, const QString& category)
{
    QList<PerformanceMetric> metrics = m_monitor->getMetrics(category);
    bool found = false;
    
    for (const PerformanceMetric& metric : metrics) {
        if (metric.name == name) {
            found = true;
            break;
        }
    }
    
    QVERIFY2(found, QString("Metric '%1' not found in category '%2'").arg(name, category).toUtf8());
}

double TestPerformanceMonitor::calculateExpectedAverage(const QList<double>& values)
{
    if (values.isEmpty()) return 0.0;
    
    double sum = 0.0;
    for (double value : values) {
        sum += value;
    }
    
    return sum / values.size();
}

void TestPerformanceMonitor::testTimerWithoutStart()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // Try to end timer without starting it
    m_monitor->endTimer("NonExistentTimer");

    // Should not emit signal or crash
    QCOMPARE(spy.count(), 0);
}

void TestPerformanceMonitor::testTimerOverwrite()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // Start timer
    m_monitor->startTimer("OverwriteTest");
    QTest::qWait(20);

    // Start same timer again (should overwrite)
    m_monitor->startTimer("OverwriteTest");
    QTest::qWait(30);

    // End timer
    m_monitor->endTimer("OverwriteTest");

    // Should record only the second timing
    QCOMPARE(spy.count(), 1);

    QList<PerformanceMetric> metrics = m_monitor->getMetrics("Performance");
    QVERIFY(metrics.size() > 0);

    // Should be closer to 30ms than 50ms
    const PerformanceMetric& metric = metrics.last();
    QVERIFY(metric.value < 45); // Less than 45ms (closer to 30ms)
}

void TestPerformanceMonitor::testGetMetrics()
{
    // Record sample metrics
    recordSampleMetrics();

    // Get all metrics
    QList<PerformanceMetric> allMetrics = m_monitor->getMetrics();
    QVERIFY(allMetrics.size() >= 5); // At least 5 metrics recorded

    // Verify metrics have valid data
    for (const PerformanceMetric& metric : allMetrics) {
        QVERIFY(!metric.name.isEmpty());
        QVERIFY(!metric.category.isEmpty());
        QVERIFY(metric.timestamp.isValid());
    }
}

void TestPerformanceMonitor::testGetMetricsByCategory()
{
    // Record sample metrics
    recordSampleMetrics();

    // Get system metrics
    QList<PerformanceMetric> systemMetrics = m_monitor->getMetrics("System");
    QVERIFY(systemMetrics.size() >= 2); // CPU and Memory

    // Get rendering metrics
    QList<PerformanceMetric> renderingMetrics = m_monitor->getMetrics("Rendering");
    QVERIFY(renderingMetrics.size() >= 1); // RenderTime

    // Get cache metrics
    QList<PerformanceMetric> cacheMetrics = m_monitor->getMetrics("Cache");
    QVERIFY(cacheMetrics.size() >= 2); // CacheHit and CacheMiss

    // Verify category filtering
    for (const PerformanceMetric& metric : systemMetrics) {
        QCOMPARE(metric.category, QString("System"));
    }
}

void TestPerformanceMonitor::testGetRecentMetrics()
{
    // Record metrics with timestamps
    m_monitor->recordMetric("Recent1", "Test", 10.0, "units");
    QTest::qWait(100);
    m_monitor->recordMetric("Recent2", "Test", 20.0, "units");

    // Get recent metrics (last 5 minutes)
    QList<PerformanceMetric> recentMetrics = m_monitor->getRecentMetrics(5);
    QVERIFY(recentMetrics.size() >= 2);

    // All should be recent
    QDateTime fiveMinutesAgo = QDateTime::currentDateTime().addSecs(-5 * 60);
    for (const PerformanceMetric& metric : recentMetrics) {
        QVERIFY(metric.timestamp >= fiveMinutesAgo);
    }
}

void TestPerformanceMonitor::testGetRenderingStats()
{
    // Record rendering data
    m_monitor->recordRenderTime(100.0);
    m_monitor->recordRenderTime(200.0);
    m_monitor->recordCacheHit();
    m_monitor->recordCacheHit();
    m_monitor->recordCacheMiss();
    m_monitor->recordActiveThreads(4);
    m_monitor->recordMemoryUsage(150 * 1024 * 1024); // 150 MB

    // Get rendering stats
    RenderingStats stats = m_monitor->getRenderingStats();

    // Verify stats
    QCOMPARE(stats.totalPagesRendered, 2);
    QCOMPARE(stats.averageRenderTime, 150.0); // (100 + 200) / 2
    QCOMPARE(stats.cacheHits, 2);
    QCOMPARE(stats.cacheMisses, 1);
    QCOMPARE(stats.activeThreads, 4);
    QCOMPARE(stats.peakMemoryUsage, 150.0);
    QVERIFY(stats.lastUpdate.isValid());

    // Test cache hit ratio calculation
    double expectedRatio = (2.0 / 3.0) * 100.0; // 66.67%
    QCOMPARE(stats.getCacheHitRatio(), expectedRatio);
}

void TestPerformanceMonitor::testGetSystemMetrics()
{
    // Start monitoring to collect system metrics
    m_monitor->startMonitoring();

    // Wait for system metrics to be collected
    QTest::qWait(1100); // Wait longer than monitoring interval

    // Get system metrics
    SystemMetrics metrics = m_monitor->getSystemMetrics();

    // Verify metrics are valid
    QVERIFY(metrics.cpuUsage >= 0.0);
    QVERIFY(metrics.memoryUsage >= 0);
    QVERIFY(metrics.availableMemory >= 0);
    QVERIFY(metrics.threadCount > 0);
    QVERIFY(metrics.timestamp.isValid());

    // Test memory usage percentage calculation
    double memoryPercent = metrics.getMemoryUsagePercent();
    QVERIFY(memoryPercent >= 0.0 && memoryPercent <= 100.0);

    m_monitor->stopMonitoring();
}

void TestPerformanceMonitor::testGetAverageMetric()
{
    // Record multiple values for the same metric
    QList<double> values = {10.0, 20.0, 30.0, 40.0, 50.0};
    for (double value : values) {
        m_monitor->recordMetric("TestAverage", "Test", value, "units");
    }

    // Calculate expected average
    double expectedAverage = calculateExpectedAverage(values);

    // Get average metric
    double actualAverage = m_monitor->getAverageMetric("TestAverage", 5);

    // Verify average calculation
    QCOMPARE(actualAverage, expectedAverage);
}

void TestPerformanceMonitor::testGetPeakMetric()
{
    // Record multiple values for the same metric
    QList<double> values = {10.0, 50.0, 30.0, 80.0, 20.0};
    for (double value : values) {
        m_monitor->recordMetric("TestPeak", "Test", value, "units");
    }

    // Get peak metric
    double peak = m_monitor->getPeakMetric("TestPeak", 5);

    // Verify peak calculation
    QCOMPARE(peak, 80.0); // Maximum value
}

void TestPerformanceMonitor::testGetCategorySummary()
{
    // Record metrics in different categories
    m_monitor->recordMetric("Metric1", "Category1", 10.0, "units");
    m_monitor->recordMetric("Metric2", "Category1", 20.0, "units");
    m_monitor->recordMetric("Metric3", "Category2", 30.0, "units");
    m_monitor->recordMetric("Metric4", "Category2", 40.0, "units");

    // Get category summary
    QMap<QString, double> summary = m_monitor->getCategorySummary();

    // Verify summary contains expected categories
    QVERIFY(summary.contains("Category1"));
    QVERIFY(summary.contains("Category2"));

    // Verify summary values (implementation-dependent)
    QVERIFY(summary["Category1"] > 0.0);
    QVERIFY(summary["Category2"] > 0.0);
}

void TestPerformanceMonitor::testOptimizationRecommendations()
{
    // Create conditions that should trigger recommendations

    // Low cache hit ratio
    for (int i = 0; i < 10; ++i) {
        m_monitor->recordCacheMiss(); // 10 misses
    }
    m_monitor->recordCacheHit(); // 1 hit (9% hit ratio)

    // High memory usage
    m_monitor->recordMemoryUsage(600 * 1024 * 1024); // 600 MB (above threshold)

    // Too many threads
    m_monitor->recordActiveThreads(12); // Above max threads

    // Get recommendations
    QStringList recommendations = m_monitor->getOptimizationRecommendations();

    // Should have recommendations
    QVERIFY(recommendations.size() > 0);

    // Verify recommendations are not empty
    for (const QString& recommendation : recommendations) {
        QVERIFY(!recommendation.isEmpty());
    }
}

void TestPerformanceMonitor::testShouldOptimizeCache()
{
    // Test with good cache hit ratio
    for (int i = 0; i < 8; ++i) {
        m_monitor->recordCacheHit(); // 8 hits
    }
    for (int i = 0; i < 2; ++i) {
        m_monitor->recordCacheMiss(); // 2 misses (80% hit ratio)
    }

    QVERIFY(!m_monitor->shouldOptimizeCache()); // Should not optimize

    // Test with poor cache hit ratio
    for (int i = 0; i < 10; ++i) {
        m_monitor->recordCacheMiss(); // 10 more misses (16.7% hit ratio)
    }

    QVERIFY(m_monitor->shouldOptimizeCache()); // Should optimize
}

void TestPerformanceMonitor::testShouldReduceThreads()
{
    // Test with normal thread count
    m_monitor->recordActiveThreads(4);
    QVERIFY(!m_monitor->shouldReduceThreads());

    // Test with high thread count
    m_monitor->recordActiveThreads(12); // Above threshold
    QVERIFY(m_monitor->shouldReduceThreads());
}

void TestPerformanceMonitor::testShouldClearMemory()
{
    // Test with normal memory usage
    m_monitor->recordMemoryUsage(100 * 1024 * 1024); // 100 MB
    QVERIFY(!m_monitor->shouldClearMemory());

    // Test with high memory usage
    m_monitor->recordMemoryUsage(600 * 1024 * 1024); // 600 MB (above threshold)
    QVERIFY(m_monitor->shouldClearMemory());
}

void TestPerformanceMonitor::testSetMonitoringInterval()
{
    // Test setting monitoring interval
    int newInterval = 2000; // 2 seconds
    m_monitor->setMonitoringInterval(newInterval);

    // Start monitoring to test interval
    m_monitor->startMonitoring();

    // Verify monitoring is active
    QVERIFY(m_monitor->isMonitoring());

    m_monitor->stopMonitoring();
}

void TestPerformanceMonitor::testSetMaxHistorySize()
{
    // Set small history size
    m_monitor->setMaxHistorySize(3);

    // Record more metrics than history size
    for (int i = 0; i < 5; ++i) {
        m_monitor->recordMetric(QString("Metric%1").arg(i), "Test", i, "units");
    }

    // Should only keep last 3 metrics
    QList<PerformanceMetric> metrics = m_monitor->getMetrics("Test");
    QVERIFY(metrics.size() <= 3);
}

void TestPerformanceMonitor::testSetMetricRetentionTime()
{
    // Test setting retention time
    m_monitor->setMetricRetentionTime(30); // 30 minutes

    // This is mainly a configuration test - actual cleanup happens over time
    QVERIFY(true); // Test passes if no crash occurs
}

void TestPerformanceMonitor::testExportMetricsToJson()
{
    // Record sample metrics
    recordSampleMetrics();

    // Export to JSON
    QString jsonString = m_monitor->exportMetricsToJson();

    // Verify JSON is valid
    QVERIFY(!jsonString.isEmpty());

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &error);
    QVERIFY(error.error == QJsonParseError::NoError);

    // Verify JSON structure
    QJsonObject root = doc.object();
    QVERIFY(root.contains("metrics"));
    QVERIFY(root.contains("exportTime"));

    QJsonArray metricsArray = root["metrics"].toArray();
    QVERIFY(metricsArray.size() > 0);

    // Verify metric structure
    QJsonObject firstMetric = metricsArray[0].toObject();
    QVERIFY(firstMetric.contains("name"));
    QVERIFY(firstMetric.contains("category"));
    QVERIFY(firstMetric.contains("value"));
    QVERIFY(firstMetric.contains("unit"));
    QVERIFY(firstMetric.contains("timestamp"));
}

void TestPerformanceMonitor::testExportMetricsToCsv()
{
    // Record sample metrics
    recordSampleMetrics();

    // Export to CSV
    QString csvString = m_monitor->exportMetricsToCsv();

    // Verify CSV is not empty
    QVERIFY(!csvString.isEmpty());

    // Verify CSV has header
    QStringList lines = csvString.split('\n', Qt::SkipEmptyParts);
    QVERIFY(lines.size() > 1); // At least header + data

    // Verify header contains expected columns
    QString header = lines.first();
    QVERIFY(header.contains("Name"));
    QVERIFY(header.contains("Category"));
    QVERIFY(header.contains("Value"));
    QVERIFY(header.contains("Unit"));
    QVERIFY(header.contains("Timestamp"));
}

void TestPerformanceMonitor::testImportMetricsFromJson()
{
    // Create JSON data
    QJsonObject root;
    QJsonArray metricsArray;

    QJsonObject metric1;
    metric1["name"] = "ImportTest1";
    metric1["category"] = "Import";
    metric1["value"] = 123.45;
    metric1["unit"] = "test";
    metric1["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    metricsArray.append(metric1);

    QJsonObject metric2;
    metric2["name"] = "ImportTest2";
    metric2["category"] = "Import";
    metric2["value"] = 67.89;
    metric2["unit"] = "test";
    metric2["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    metricsArray.append(metric2);

    root["metrics"] = metricsArray;
    root["exportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(root);
    QString jsonString = doc.toJson();

    // Import metrics
    bool success = m_monitor->importMetricsFromJson(jsonString);
    QVERIFY(success);

    // Verify imported metrics
    QList<PerformanceMetric> importedMetrics = m_monitor->getMetrics("Import");
    QCOMPARE(importedMetrics.size(), 2);

    // Verify metric data
    bool found1 = false, found2 = false;
    for (const PerformanceMetric& metric : importedMetrics) {
        if (metric.name == "ImportTest1" && metric.value == 123.45) {
            found1 = true;
        }
        if (metric.name == "ImportTest2" && metric.value == 67.89) {
            found2 = true;
        }
    }
    QVERIFY(found1);
    QVERIFY(found2);
}

void TestPerformanceMonitor::testExportImportRoundTrip()
{
    // Record sample metrics
    recordSampleMetrics();

    // Export metrics
    QString jsonString = m_monitor->exportMetricsToJson();

    // Create new monitor and import
    PerformanceMonitor* newMonitor = new PerformanceMonitor(this);
    bool success = newMonitor->importMetricsFromJson(jsonString);
    QVERIFY(success);

    // Compare metrics
    QList<PerformanceMetric> originalMetrics = m_monitor->getMetrics();
    QList<PerformanceMetric> importedMetrics = newMonitor->getMetrics();

    // Should have same number of metrics
    QCOMPARE(importedMetrics.size(), originalMetrics.size());

    delete newMonitor;
}

void TestPerformanceMonitor::testMetricsUpdatedSignal()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // Record various metrics
    m_monitor->recordMetric("Test1", "Category", 10.0, "units");
    m_monitor->recordRenderTime(100.0);
    m_monitor->recordCacheHit();
    m_monitor->recordMemoryUsage(50 * 1024 * 1024);

    // Should have emitted signal for each metric
    QCOMPARE(spy.count(), 4);
}

void TestPerformanceMonitor::testPerformanceAlertSignal()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::performanceAlert);

    // Start monitoring to enable threshold checking
    m_monitor->startMonitoring();

    // Wait for system metrics and threshold checking
    QTest::qWait(1100);

    // The signal may or may not be emitted depending on actual system performance
    // This test verifies the signal exists and can be connected
    QVERIFY(spy.isValid());

    m_monitor->stopMonitoring();
}

void TestPerformanceMonitor::testOptimizationRecommendedSignal()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::optimizationRecommended);

    // Create conditions that should trigger recommendations
    for (int i = 0; i < 20; ++i) {
        m_monitor->recordCacheMiss(); // Poor cache performance
    }
    m_monitor->recordCacheHit(); // Very low hit ratio

    // Get recommendations (which should emit signals)
    QStringList recommendations = m_monitor->getOptimizationRecommendations();

    // Should have emitted signals for recommendations
    QVERIFY(spy.count() >= 0); // May vary based on implementation
}

void TestPerformanceMonitor::testMemoryThresholdExceededSignal()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::memoryThresholdExceeded);

    // Record memory usage above threshold
    qint64 highMemory = 600 * 1024 * 1024; // 600 MB (above 500 MB threshold)
    m_monitor->recordMemoryUsage(highMemory);

    // Should have emitted threshold exceeded signal
    QCOMPARE(spy.count(), 1);

    // Verify signal arguments
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toLongLong(), highMemory);
    QVERIFY(arguments.at(1).toLongLong() > 0); // Threshold value
}

void TestPerformanceMonitor::testRenderingPerformanceChangedSignal()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::renderingPerformanceChanged);

    // Record render time (should emit signal)
    m_monitor->recordRenderTime(150.0);

    // Should have emitted rendering performance changed signal
    QCOMPARE(spy.count(), 1);

    // Verify signal argument
    QList<QVariant> arguments = spy.takeFirst();
    QVariant statsVariant = arguments.at(0);
    QVERIFY(statsVariant.canConvert<RenderingStats>());
}

void TestPerformanceMonitor::testSystemMetricsCollection()
{
    // Start monitoring
    m_monitor->startMonitoring();

    // Wait for system metrics collection
    QTest::qWait(1100);

    // Get system metrics
    SystemMetrics metrics = m_monitor->getSystemMetrics();

    // Verify metrics are collected
    QVERIFY(metrics.timestamp.isValid());
    QVERIFY(metrics.cpuUsage >= 0.0);
    QVERIFY(metrics.memoryUsage >= 0);
    QVERIFY(metrics.threadCount > 0);

    m_monitor->stopMonitoring();
}

void TestPerformanceMonitor::testSystemMetricsHistory()
{
    // Start monitoring
    m_monitor->startMonitoring();

    // Wait for multiple metric collections
    QTest::qWait(2100); // Wait for at least 2 collections

    // System metrics should be recorded as regular metrics
    QList<PerformanceMetric> systemMetrics = m_monitor->getMetrics("System");
    QVERIFY(systemMetrics.size() > 0);

    // Should have CPU, Memory, and Thread metrics
    bool hasCpu = false, hasMemory = false, hasThreads = false;
    for (const PerformanceMetric& metric : systemMetrics) {
        if (metric.name == "CPU") hasCpu = true;
        if (metric.name == "Memory") hasMemory = true;
        if (metric.name == "Threads") hasThreads = true;
    }

    QVERIFY(hasCpu);
    QVERIFY(hasMemory);
    QVERIFY(hasThreads);

    m_monitor->stopMonitoring();
}

void TestPerformanceMonitor::testPerformanceThresholds()
{
    QSignalSpy alertSpy(m_monitor, &PerformanceMonitor::performanceAlert);

    // Start monitoring
    m_monitor->startMonitoring();

    // Record high memory usage to trigger threshold
    m_monitor->recordMemoryUsage(600 * 1024 * 1024); // Above threshold

    // Wait for threshold checking
    QTest::qWait(1100);

    // Should have triggered memory threshold
    QSignalSpy memorySpy(m_monitor, &PerformanceMonitor::memoryThresholdExceeded);
    m_monitor->recordMemoryUsage(700 * 1024 * 1024); // Even higher
    QCOMPARE(memorySpy.count(), 1);

    m_monitor->stopMonitoring();
}

void TestPerformanceMonitor::testConcurrentMetricRecording()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // Record metrics from multiple "threads" (simulated with rapid calls)
    for (int i = 0; i < 100; ++i) {
        m_monitor->recordMetric(QString("Concurrent%1").arg(i), "Concurrency", i, "units");
    }

    // All metrics should be recorded
    QCOMPARE(spy.count(), 100);

    // Verify all metrics are present
    QList<PerformanceMetric> metrics = m_monitor->getMetrics("Concurrency");
    QCOMPARE(metrics.size(), 100);
}

void TestPerformanceMonitor::testConcurrentTimerOperations()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // Start multiple timers concurrently
    QStringList operations;
    for (int i = 0; i < 10; ++i) {
        QString operation = QString("ConcurrentTimer%1").arg(i);
        operations.append(operation);
        m_monitor->startTimer(operation);
    }

    QTest::qWait(50);

    // End all timers
    for (const QString& operation : operations) {
        m_monitor->endTimer(operation);
    }

    // All timers should have recorded metrics
    QCOMPARE(spy.count(), 10);
}

void TestPerformanceMonitor::testNegativeValues()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // Record negative values (should be handled gracefully)
    m_monitor->recordMetric("NegativeTest", "Test", -10.5, "units");

    // Should still record the metric
    QCOMPARE(spy.count(), 1);

    QList<PerformanceMetric> metrics = m_monitor->getMetrics("Test");
    QCOMPARE(metrics.size(), 1);
    QCOMPARE(metrics.first().value, -10.5);
}

void TestPerformanceMonitor::testExtremeValues()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // Record extreme values
    double extremeValue = 1e10; // Very large number
    m_monitor->recordMetric("ExtremeTest", "Test", extremeValue, "units");

    // Should handle extreme values
    QCOMPARE(spy.count(), 1);

    QList<PerformanceMetric> metrics = m_monitor->getMetrics("Test");
    QCOMPARE(metrics.size(), 1);
    QCOMPARE(metrics.first().value, extremeValue);
}

void TestPerformanceMonitor::testEmptyMetrics()
{
    // Test getting metrics when none exist
    QList<PerformanceMetric> metrics = m_monitor->getMetrics();
    QCOMPARE(metrics.size(), 0);

    QList<PerformanceMetric> categoryMetrics = m_monitor->getMetrics("NonExistent");
    QCOMPARE(categoryMetrics.size(), 0);

    // Test statistics with no data
    double average = m_monitor->getAverageMetric("NonExistent", 5);
    QCOMPARE(average, 0.0);

    double peak = m_monitor->getPeakMetric("NonExistent", 5);
    QCOMPARE(peak, 0.0);
}

void TestPerformanceMonitor::testInvalidTimerOperations()
{
    QSignalSpy spy(m_monitor, &PerformanceMonitor::metricsUpdated);

    // End timer that was never started
    m_monitor->endTimer("NeverStarted");
    QCOMPARE(spy.count(), 0);

    // Start timer with empty name
    m_monitor->startTimer("");
    m_monitor->endTimer("");
    QCOMPARE(spy.count(), 1); // Should still work with empty name
}

void TestPerformanceMonitor::testCompleteMonitoringWorkflow()
{
    QSignalSpy metricsSpy(m_monitor, &PerformanceMonitor::metricsUpdated);
    QSignalSpy renderingSpy(m_monitor, &PerformanceMonitor::renderingPerformanceChanged);

    // Start monitoring
    m_monitor->startMonitoring();
    QVERIFY(m_monitor->isMonitoring());

    // Record various performance data
    m_monitor->recordRenderTime(120.0);
    m_monitor->recordRenderTime(180.0);
    m_monitor->recordCacheHit();
    m_monitor->recordCacheHit();
    m_monitor->recordCacheMiss();
    m_monitor->recordMemoryUsage(200 * 1024 * 1024);
    m_monitor->recordActiveThreads(6);

    // Use performance timers
    m_monitor->startTimer("CompleteWorkflow");
    QTest::qWait(30);
    m_monitor->endTimer("CompleteWorkflow");

    // Wait for system metrics collection
    QTest::qWait(1100);

    // Verify data collection
    QVERIFY(metricsSpy.count() >= 6); // At least 6 metrics recorded
    QCOMPARE(renderingSpy.count(), 2); // 2 render times recorded

    // Verify rendering stats
    RenderingStats stats = m_monitor->getRenderingStats();
    QCOMPARE(stats.totalPagesRendered, 2);
    QCOMPARE(stats.averageRenderTime, 150.0); // (120 + 180) / 2
    QCOMPARE(stats.cacheHits, 2);
    QCOMPARE(stats.cacheMisses, 1);
    QCOMPARE(stats.activeThreads, 6);

    // Get optimization recommendations
    QStringList recommendations = m_monitor->getOptimizationRecommendations();
    QVERIFY(recommendations.size() >= 0); // May have recommendations

    // Export data
    QString jsonExport = m_monitor->exportMetricsToJson();
    QVERIFY(!jsonExport.isEmpty());

    QString csvExport = m_monitor->exportMetricsToCsv();
    QVERIFY(!csvExport.isEmpty());

    // Stop monitoring
    m_monitor->stopMonitoring();
    QVERIFY(!m_monitor->isMonitoring());
}

void TestPerformanceMonitor::testPerformanceAnalysis()
{
    // Record performance data over time
    QList<double> renderTimes = {100.0, 150.0, 200.0, 120.0, 180.0};
    for (double time : renderTimes) {
        m_monitor->recordRenderTime(time);
    }

    // Record cache performance
    for (int i = 0; i < 7; ++i) {
        m_monitor->recordCacheHit();
    }
    for (int i = 0; i < 3; ++i) {
        m_monitor->recordCacheMiss();
    }

    // Analyze performance
    RenderingStats stats = m_monitor->getRenderingStats();
    QCOMPARE(stats.totalPagesRendered, 5);
    QCOMPARE(stats.averageRenderTime, 150.0); // Average of render times
    QCOMPARE(stats.getCacheHitRatio(), 70.0); // 7/(7+3) * 100

    // Test optimization recommendations
    QVERIFY(!m_monitor->shouldOptimizeCache()); // 70% hit ratio is good

    // Test statistics
    double avgRenderTime = m_monitor->getAverageMetric("RenderTime", 5);
    QCOMPARE(avgRenderTime, 150.0);

    double peakRenderTime = m_monitor->getPeakMetric("RenderTime", 5);
    QCOMPARE(peakRenderTime, 200.0);
}

void TestPerformanceMonitor::testMetricCleanup()
{
    // Set small history size for testing cleanup
    m_monitor->setMaxHistorySize(5);

    // Record more metrics than history size
    for (int i = 0; i < 10; ++i) {
        m_monitor->recordMetric(QString("Cleanup%1").arg(i), "Cleanup", i, "units");
    }

    // Should only keep the last 5 metrics
    QList<PerformanceMetric> metrics = m_monitor->getMetrics("Cleanup");
    QVERIFY(metrics.size() <= 5);

    // Verify the kept metrics are the most recent ones
    if (metrics.size() == 5) {
        // Should have metrics 5, 6, 7, 8, 9
        QSet<double> expectedValues = {5.0, 6.0, 7.0, 8.0, 9.0};
        QSet<double> actualValues;

        for (const PerformanceMetric& metric : metrics) {
            actualValues.insert(metric.value);
        }

        QCOMPARE(actualValues, expectedValues);
    }
}

QTEST_MAIN(TestPerformanceMonitor)
#include "test_performance_monitor.moc"
