#include "ErrorHandler.h"
#include "CrashLogPackager.h"
#include "Logger.h"
#include <QApplication>
#include <QMessageBox>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QSysInfo>
#include <QThread>
#include <QCoreApplication>
#include <QFileInfo>
#include <QTextStream>
#include <QDateTime>
#include <QMutexLocker>
#include <QProcess>
#include <exception>
#include <new>
#include <csignal>
#include <cstdlib>
// #include <cpptrace/cpptrace.hpp> // Temporarily disabled due to build issues

#ifdef Q_OS_WIN
#include <windows.h>
#include <psapi.h>
#include <dbghelp.h>
#elif defined(Q_OS_UNIX)
#include <sys/resource.h>
#include <unistd.h>
#endif

ErrorHandler* ErrorHandler::s_instance = nullptr;
QMutex ErrorHandler::s_crashMutex;
bool ErrorHandler::s_crashHandlerInstalled = false;

// Signal handler function declarations
extern "C" {
    void signalHandler(int signal);
}

// Terminate handler function
void terminateHandler();

// Signal handler implementation
void signalHandler(int signal) {
    QMutexLocker locker(&ErrorHandler::s_crashMutex);

    if (ErrorHandler::s_instance) {
        ErrorHandler::s_instance->handleSignalCrash(signal);
    }

    // Re-raise the signal with default handler
    std::signal(signal, SIG_DFL);
    std::raise(signal);
}

// Terminate handler implementation
void terminateHandler() {
    if (ErrorHandler::s_instance) {
        ErrorHandler::s_instance->handleTerminate();
    }

    // Call the default terminate handler
    std::abort();
}

ErrorHandler* ErrorHandler::instance()
{
    if (!s_instance) {
        s_instance = new ErrorHandler();
    }
    return s_instance;
}

ErrorHandler::ErrorHandler(QObject *parent)
    : QObject(parent)
    , m_showUserNotifications(true)
    , m_autoReporting(false)
    , m_maxErrorHistory(DEFAULT_MAX_HISTORY)
    , m_notificationTimer(new QTimer(this))
    , m_crashLogPackager(new CrashLogPackager(this))
{
    m_notificationTimer->setSingleShot(true);
    m_notificationTimer->setInterval(NOTIFICATION_DELAY);

    // Initialize crash log directory
    ensureCrashLogDirectory();

    // Install Qt message handler
    qInstallMessageHandler([](QtMsgType type, const QMessageLogContext& context, const QString& message) {
        if (ErrorHandler::s_instance) {
            ErrorHandler::s_instance->handleQtMessage(type, context, message);
        }
    });
}

ErrorHandler::~ErrorHandler()
{
    qInstallMessageHandler(nullptr);
    uninstallSignalHandlers();
}

void ErrorHandler::reportError(ErrorSeverity severity, ErrorCategory category, 
                              const QString& title, const QString& message, 
                              const QString& details, const QString& location)
{
    ErrorInfo error;
    error.severity = severity;
    error.category = category;
    error.title = title;
    error.message = message;
    error.details = details;
    error.location = location;
    error.timestamp = QDateTime::currentDateTime();
    error.userNotified = false;
    
    // Add to history
    m_errorHistory.append(error);
    trimErrorHistory();
    
    // Log the error
    logError(error);
    
    // Emit signal
    emit errorReported(error);
    
    // Handle critical errors
    if (severity >= ErrorSeverity::Critical) {
        emit criticalErrorOccurred(error);
    }
    
    // Show user notification if enabled
    if (m_showUserNotifications) {
        notifyUser(error);
    }
    
    // Attempt auto-recovery if possible
    if (m_autoReporting && canAutoRecover(category, message)) {
        attemptAutoRecovery(error);
    }
}

void ErrorHandler::reportException(const QException& exception, const QString& location)
{
    reportError(ErrorSeverity::Error, ErrorCategory::System, 
               "Exception Occurred", exception.what(), QString(), location);
}

void ErrorHandler::reportSystemError(const QString& operation, int errorCode, const QString& location)
{
    reportError(ErrorSeverity::Error, ErrorCategory::System,
               "System Error", 
               QString("Operation '%1' failed with error code %2").arg(operation).arg(errorCode),
               QString(), location);
}

void ErrorHandler::reportFileError(const QString& filePath, const QString& operation, const QString& location)
{
    reportError(ErrorSeverity::Error, ErrorCategory::FileIO,
               "File Operation Failed",
               QString("Failed to %1 file: %2").arg(operation, filePath),
               QString(), location);
}

void ErrorHandler::reportPdfError(const QString& pdfPath, const QString& operation, const QString& location)
{
    reportError(ErrorSeverity::Error, ErrorCategory::PDF,
               "PDF Operation Failed",
               QString("Failed to %1 PDF: %2").arg(operation, pdfPath),
               QString(), location);
}

void ErrorHandler::setShowUserNotifications(bool enabled)
{
    m_showUserNotifications = enabled;
}

bool ErrorHandler::getShowUserNotifications() const
{
    return m_showUserNotifications;
}

void ErrorHandler::setAutoReporting(bool enabled)
{
    m_autoReporting = enabled;
}

bool ErrorHandler::getAutoReporting() const
{
    return m_autoReporting;
}

void ErrorHandler::setMaxErrorHistory(int maxErrors)
{
    m_maxErrorHistory = maxErrors;
    trimErrorHistory();
}

int ErrorHandler::getMaxErrorHistory() const
{
    return m_maxErrorHistory;
}

QList<ErrorInfo> ErrorHandler::getErrorHistory() const
{
    return m_errorHistory;
}

void ErrorHandler::clearErrorHistory()
{
    m_errorHistory.clear();
}

ErrorInfo ErrorHandler::getLastError() const
{
    return m_errorHistory.isEmpty() ? ErrorInfo() : m_errorHistory.last();
}

int ErrorHandler::getErrorCount(ErrorSeverity severity) const
{
    int count = 0;
    for (const auto& error : m_errorHistory) {
        if (error.severity == severity) {
            count++;
        }
    }
    return count;
}

QString ErrorHandler::getRecoverySuggestion(ErrorCategory category, const QString& errorMessage) const
{
    Q_UNUSED(errorMessage)
    
    switch (category) {
    case ErrorCategory::FileIO:
        return "Check file permissions and disk space. Try saving to a different location.";
    case ErrorCategory::PDF:
        return "Verify the PDF file is not corrupted. Try opening a different PDF file.";
    case ErrorCategory::Memory:
        return "Close other applications to free up memory. Restart the application if needed.";
    case ErrorCategory::Network:
        return "Check your internet connection and try again.";
    default:
        return "Try restarting the application. Contact support if the problem persists.";
    }
}

bool ErrorHandler::canAutoRecover(ErrorCategory category, const QString& errorMessage) const
{
    Q_UNUSED(errorMessage)
    
    // Simple auto-recovery logic
    return category == ErrorCategory::UI || category == ErrorCategory::Memory;
}

void ErrorHandler::attemptAutoRecovery(const ErrorInfo& error)
{
    bool success = false;
    
    switch (error.category) {
    case ErrorCategory::UI:
        // Try to refresh UI
        if (QApplication::activeWindow()) {
            QApplication::activeWindow()->update();
            success = true;
        }
        break;
    case ErrorCategory::Memory:
        // Force garbage collection
        QApplication::processEvents();
        success = true;
        break;
    default:
        break;
    }
    
    emit recoveryAttempted(error, success);
}

void ErrorHandler::showErrorDialog(const ErrorInfo& error)
{
    QMessageBox msgBox;
    msgBox.setIcon(severityToIcon(error.severity));
    msgBox.setWindowTitle("Error - " + error.title);
    msgBox.setText(error.message);
    
    if (!error.details.isEmpty()) {
        msgBox.setDetailedText(error.details);
    }
    
    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.exec();
}

void ErrorHandler::showErrorNotification(const ErrorInfo& error)
{
    // For now, just show a simple message box
    // In a real application, this could be a toast notification
    showErrorDialog(error);
}

void ErrorHandler::setupCrashHandler()
{
    if (s_crashHandlerInstalled) {
        return;
    }

    // Install signal handlers for crash detection
    installSignalHandlers();

    // Install terminate handler for uncaught exceptions
    std::set_terminate(terminateHandler);

    // Install new handler for memory allocation failures
    std::set_new_handler([]() {
        if (ErrorHandler::s_instance) {
            ErrorHandler::s_instance->handleUnexpectedException();
        }
        throw std::bad_alloc();
    });

    s_crashHandlerInstalled = true;

    Logger* logger = Logger::instance();
    logger->info("Comprehensive crash handler with cpptrace initialized", "ErrorHandler");
    qDebug() << "Crash handler setup completed with cpptrace integration";
}

void ErrorHandler::handleCrash(const QString& crashInfo)
{
    reportError(ErrorSeverity::Fatal, ErrorCategory::System,
               "Application Crash", "The application has crashed unexpectedly",
               crashInfo, "CrashHandler");
}

void ErrorHandler::handleQtMessage(QtMsgType type, const QMessageLogContext& context, const QString& message)
{
    ErrorSeverity severity = ErrorSeverity::Info; // Initialize with default value
    switch (type) {
    case QtDebugMsg:
        return; // Don't report debug messages
    case QtInfoMsg:
        severity = ErrorSeverity::Info;
        break;
    case QtWarningMsg:
        severity = ErrorSeverity::Warning;
        break;
    case QtCriticalMsg:
        severity = ErrorSeverity::Critical;
        break;
    case QtFatalMsg:
        severity = ErrorSeverity::Fatal;
        break;
    }
    
    QString location = QString("%1:%2").arg(context.file ? context.file : "unknown")
                                      .arg(context.line);
    
    reportError(severity, ErrorCategory::System, "Qt Message", message, QString(), location);
}

void ErrorHandler::notifyUser(const ErrorInfo& error)
{
    if (error.severity >= ErrorSeverity::Error) {
        // Use timer to avoid blocking the current operation
        QTimer::singleShot(NOTIFICATION_DELAY, [this, error]() {
            showErrorNotification(error);
        });
    }
}

void ErrorHandler::logError(const ErrorInfo& error)
{
    QString logMessage = QString("[%1] %2: %3 - %4")
                        .arg(severityToString(error.severity))
                        .arg(categoryToString(error.category))
                        .arg(error.title)
                        .arg(error.message);
    
    if (!error.location.isEmpty()) {
        logMessage += QString(" (at %1)").arg(error.location);
    }
    
    qDebug() << logMessage;
}

void ErrorHandler::trimErrorHistory()
{
    while (m_errorHistory.size() > m_maxErrorHistory) {
        m_errorHistory.removeFirst();
    }
}

QString ErrorHandler::severityToString(ErrorSeverity severity) const
{
    switch (severity) {
    case ErrorSeverity::Info: return "INFO";
    case ErrorSeverity::Warning: return "WARNING";
    case ErrorSeverity::Error: return "ERROR";
    case ErrorSeverity::Critical: return "CRITICAL";
    case ErrorSeverity::Fatal: return "FATAL";
    }
    return "UNKNOWN";
}

QString ErrorHandler::categoryToString(ErrorCategory category) const
{
    switch (category) {
    case ErrorCategory::System: return "SYSTEM";
    case ErrorCategory::FileIO: return "FILE_IO";
    case ErrorCategory::PDF: return "PDF";
    case ErrorCategory::UI: return "UI";
    case ErrorCategory::Network: return "NETWORK";
    case ErrorCategory::Memory: return "MEMORY";
    case ErrorCategory::Unknown: return "UNKNOWN";
    }
    return "UNKNOWN";
}

QMessageBox::Icon ErrorHandler::severityToIcon(ErrorSeverity severity) const
{
    switch (severity) {
    case ErrorSeverity::Info: return QMessageBox::Information;
    case ErrorSeverity::Warning: return QMessageBox::Warning;
    case ErrorSeverity::Error: return QMessageBox::Critical;
    case ErrorSeverity::Critical: return QMessageBox::Critical;
    case ErrorSeverity::Fatal: return QMessageBox::Critical;
    }
    return QMessageBox::NoIcon;
}

// Enhanced crash handling implementation
void ErrorHandler::installSignalHandlers()
{
#ifdef Q_OS_WIN
    // Windows signal handling
    std::signal(SIGABRT, signalHandler);
    std::signal(SIGFPE, signalHandler);
    std::signal(SIGILL, signalHandler);
    std::signal(SIGINT, signalHandler);
    std::signal(SIGSEGV, signalHandler);
    std::signal(SIGTERM, signalHandler);
#else
    // Unix/Linux signal handling
    std::signal(SIGABRT, signalHandler);
    std::signal(SIGFPE, signalHandler);
    std::signal(SIGILL, signalHandler);
    std::signal(SIGINT, signalHandler);
    std::signal(SIGSEGV, signalHandler);
    std::signal(SIGTERM, signalHandler);
    std::signal(SIGBUS, signalHandler);
    std::signal(SIGQUIT, signalHandler);
    std::signal(SIGTRAP, signalHandler);
#endif
}

void ErrorHandler::uninstallSignalHandlers()
{
    if (!s_crashHandlerInstalled) {
        return;
    }

#ifdef Q_OS_WIN
    std::signal(SIGABRT, SIG_DFL);
    std::signal(SIGFPE, SIG_DFL);
    std::signal(SIGILL, SIG_DFL);
    std::signal(SIGINT, SIG_DFL);
    std::signal(SIGSEGV, SIG_DFL);
    std::signal(SIGTERM, SIG_DFL);
#else
    std::signal(SIGABRT, SIG_DFL);
    std::signal(SIGFPE, SIG_DFL);
    std::signal(SIGILL, SIG_DFL);
    std::signal(SIGINT, SIG_DFL);
    std::signal(SIGSEGV, SIG_DFL);
    std::signal(SIGTERM, SIG_DFL);
    std::signal(SIGBUS, SIG_DFL);
    std::signal(SIGQUIT, SIG_DFL);
    std::signal(SIGTRAP, SIG_DFL);
#endif

    s_crashHandlerInstalled = false;
}

void ErrorHandler::handleSignalCrash(int signal)
{
    CrashInfo crashInfo;
    crashInfo.crashType = "Signal";
    crashInfo.timestamp = QDateTime::currentDateTime();
    crashInfo.processId = QString::number(QCoreApplication::applicationPid());
    crashInfo.threadId = QString::number(reinterpret_cast<quintptr>(QThread::currentThreadId()));

    // Map signal to string
    switch (signal) {
        case SIGABRT: crashInfo.signal = "SIGABRT (Abort)"; break;
        case SIGFPE: crashInfo.signal = "SIGFPE (Floating Point Exception)"; break;
        case SIGILL: crashInfo.signal = "SIGILL (Illegal Instruction)"; break;
        case SIGINT: crashInfo.signal = "SIGINT (Interrupt)"; break;
        case SIGSEGV: crashInfo.signal = "SIGSEGV (Segmentation Violation)"; break;
        case SIGTERM: crashInfo.signal = "SIGTERM (Termination)"; break;
#ifndef Q_OS_WIN
        case SIGBUS: crashInfo.signal = "SIGBUS (Bus Error)"; break;
        case SIGQUIT: crashInfo.signal = "SIGQUIT (Quit)"; break;
        case SIGTRAP: crashInfo.signal = "SIGTRAP (Trap)"; break;
#endif
        default: crashInfo.signal = QString("Unknown Signal (%1)").arg(signal); break;
    }

    // Generate stack trace (fallback implementation)
    crashInfo.stackTrace = QString("Stack trace not available (cpptrace disabled)\nSignal: %1\nLocation: %2")
        .arg(crashInfo.signal)
        .arg(Q_FUNC_INFO);

    // Gather system information
    crashInfo.systemInfo = getSystemInfo();
    crashInfo.memoryInfo = getMemoryInfo();
    crashInfo.buildInfo = getBuildInfo();
    crashInfo.userActions = getUserActionHistory();

    // Save crash report
    saveCrashReport(crashInfo);

    // Emit signal for UI handling
    emit crashDetected(crashInfo);

    // Log the crash
    Logger* logger = Logger::instance();
    logger->error(QString("Application crashed with signal: %1").arg(crashInfo.signal), "CrashHandler");
}

void ErrorHandler::handleTerminate()
{
    CrashInfo crashInfo;
    crashInfo.crashType = "Terminate";
    crashInfo.signal = "std::terminate called";
    crashInfo.timestamp = QDateTime::currentDateTime();
    crashInfo.processId = QString::number(QCoreApplication::applicationPid());
    crashInfo.threadId = QString::number(reinterpret_cast<quintptr>(QThread::currentThreadId()));

    // Generate stack trace (fallback implementation)
    crashInfo.stackTrace = QString("Stack trace not available (cpptrace disabled)\nTerminate called\nLocation: %1")
        .arg(Q_FUNC_INFO);

    // Gather system information
    crashInfo.systemInfo = getSystemInfo();
    crashInfo.memoryInfo = getMemoryInfo();
    crashInfo.buildInfo = getBuildInfo();
    crashInfo.userActions = getUserActionHistory();

    // Save crash report
    saveCrashReport(crashInfo);

    // Emit signal for UI handling
    emit crashDetected(crashInfo);

    // Log the crash
    Logger* logger = Logger::instance();
    logger->error("Application terminated unexpectedly", "CrashHandler");
}

void ErrorHandler::handleUnexpectedException()
{
    CrashInfo crashInfo;
    crashInfo.crashType = "Memory";
    crashInfo.signal = "Memory allocation failure";
    crashInfo.timestamp = QDateTime::currentDateTime();
    crashInfo.processId = QString::number(QCoreApplication::applicationPid());
    crashInfo.threadId = QString::number(reinterpret_cast<quintptr>(QThread::currentThreadId()));

    // Generate stack trace (fallback implementation)
    crashInfo.stackTrace = QString("Stack trace not available (cpptrace disabled)\nMemory allocation failure\nLocation: %1")
        .arg(Q_FUNC_INFO);

    // Gather system information
    crashInfo.systemInfo = getSystemInfo();
    crashInfo.memoryInfo = getMemoryInfo();
    crashInfo.buildInfo = getBuildInfo();
    crashInfo.userActions = getUserActionHistory();

    // Save crash report
    saveCrashReport(crashInfo);

    // Emit signal for UI handling
    emit crashDetected(crashInfo);

    // Log the crash
    Logger* logger = Logger::instance();
    logger->error("Memory allocation failure detected", "CrashHandler");
}

QString ErrorHandler::getSystemInfo()
{
    QJsonObject systemInfo;

    systemInfo["os"] = QSysInfo::prettyProductName();
    systemInfo["kernel"] = QSysInfo::kernelType() + " " + QSysInfo::kernelVersion();
    systemInfo["architecture"] = QSysInfo::currentCpuArchitecture();
    systemInfo["hostname"] = QSysInfo::machineHostName();
    systemInfo["qt_version"] = QT_VERSION_STR;
    systemInfo["app_name"] = QCoreApplication::applicationName();
    systemInfo["app_version"] = QCoreApplication::applicationVersion();
    systemInfo["build_abi"] = QSysInfo::buildAbi();

    QJsonDocument doc(systemInfo);
    return doc.toJson(QJsonDocument::Compact);
}

QString ErrorHandler::getMemoryInfo()
{
    QJsonObject memoryInfo;

#ifdef Q_OS_WIN
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    if (GlobalMemoryStatusEx(&memStatus)) {
        memoryInfo["total_physical"] = static_cast<qint64>(memStatus.ullTotalPhys);
        memoryInfo["available_physical"] = static_cast<qint64>(memStatus.ullAvailPhys);
        memoryInfo["total_virtual"] = static_cast<qint64>(memStatus.ullTotalVirtual);
        memoryInfo["available_virtual"] = static_cast<qint64>(memStatus.ullAvailVirtual);
        memoryInfo["memory_load"] = static_cast<int>(memStatus.dwMemoryLoad);
    }

    PROCESS_MEMORY_COUNTERS_EX pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
        memoryInfo["working_set"] = static_cast<qint64>(pmc.WorkingSetSize);
        memoryInfo["peak_working_set"] = static_cast<qint64>(pmc.PeakWorkingSetSize);
        memoryInfo["private_usage"] = static_cast<qint64>(pmc.PrivateUsage);
    }
#elif defined(Q_OS_UNIX)
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        memoryInfo["max_resident_set"] = static_cast<qint64>(usage.ru_maxrss * 1024); // Convert KB to bytes on Linux
        memoryInfo["page_faults"] = static_cast<qint64>(usage.ru_majflt);
        memoryInfo["minor_page_faults"] = static_cast<qint64>(usage.ru_minflt);
    }
#endif

    QJsonDocument doc(memoryInfo);
    return doc.toJson(QJsonDocument::Compact);
}

QString ErrorHandler::getBuildInfo()
{
    QJsonObject buildInfo;

    buildInfo["compiler"] =
#ifdef Q_CC_MSVC
        QString("MSVC %1").arg(_MSC_VER);
#elif defined(Q_CC_GNU)
        QString("GCC %1.%2.%3").arg(__GNUC__).arg(__GNUC_MINOR__).arg(__GNUC_PATCHLEVEL__);
#elif defined(Q_CC_CLANG)
        QString("Clang %1.%2.%3").arg(__clang_major__).arg(__clang_minor__).arg(__clang_patchlevel__);
#else
        "Unknown";
#endif

    buildInfo["build_type"] =
#ifdef QT_DEBUG
        "Debug";
#else
        "Release";
#endif

    buildInfo["build_date"] = __DATE__;
    buildInfo["build_time"] = __TIME__;
    buildInfo["qt_version"] = QT_VERSION_STR;

    QJsonDocument doc(buildInfo);
    return doc.toJson(QJsonDocument::Compact);
}

QString ErrorHandler::getUserActionHistory()
{
    QMutexLocker locker(&m_userActionMutex);

    QJsonArray actions;
    for (const QString& action : m_userActionHistory) {
        actions.append(action);
    }

    QJsonObject actionHistory;
    actionHistory["actions"] = actions;
    actionHistory["count"] = actions.size();

    QJsonDocument doc(actionHistory);
    return doc.toJson(QJsonDocument::Compact);
}

void ErrorHandler::trackUserAction(const QString& action)
{
    QMutexLocker locker(&m_userActionMutex);

    QString timestampedAction = QString("[%1] %2")
        .arg(QDateTime::currentDateTime().toString(Qt::ISODate))
        .arg(action);

    m_userActionHistory.append(timestampedAction);

    // Keep only the last MAX_USER_ACTIONS
    while (m_userActionHistory.size() > MAX_USER_ACTIONS) {
        m_userActionHistory.removeFirst();
    }
}

QString ErrorHandler::getCurrentStackTrace()
{
    // Fallback implementation without cpptrace
    return QString("Stack trace not available (cpptrace disabled)\nCalled from: %1")
        .arg(Q_FUNC_INFO);
}

QString ErrorHandler::formatStackTrace(const QString& fallbackTrace)
{
    // Fallback implementation without cpptrace
    return fallbackTrace.isEmpty() ? "Stack trace formatting not available (cpptrace disabled)" : fallbackTrace;
}

QString ErrorHandler::generateCrashReport(const CrashInfo& crashInfo)
{
    QJsonObject report;

    // Basic crash information
    report["crash_type"] = crashInfo.crashType;
    report["signal"] = crashInfo.signal;
    report["timestamp"] = crashInfo.timestamp.toString(Qt::ISODate);
    report["process_id"] = crashInfo.processId;
    report["thread_id"] = crashInfo.threadId;

    // System information
    QJsonDocument systemDoc = QJsonDocument::fromJson(crashInfo.systemInfo.toUtf8());
    report["system_info"] = systemDoc.object();

    // Memory information
    QJsonDocument memoryDoc = QJsonDocument::fromJson(crashInfo.memoryInfo.toUtf8());
    report["memory_info"] = memoryDoc.object();

    // Build information
    QJsonDocument buildDoc = QJsonDocument::fromJson(crashInfo.buildInfo.toUtf8());
    report["build_info"] = buildDoc.object();

    // User actions
    QJsonDocument actionsDoc = QJsonDocument::fromJson(crashInfo.userActions.toUtf8());
    report["user_actions"] = actionsDoc.object();

    // Stack trace
    report["stack_trace"] = crashInfo.stackTrace;

    QJsonDocument reportDoc(report);
    return reportDoc.toJson(QJsonDocument::Indented);
}

bool ErrorHandler::saveCrashReport(const CrashInfo& crashInfo)
{
    QString reportContent = generateCrashReport(crashInfo);

    // Generate unique filename
    QString filename = QString("crash_%1_%2.json")
        .arg(crashInfo.timestamp.toString("yyyyMMdd_hhmmss"))
        .arg(crashInfo.processId);

    QString filePath = QDir(m_crashLogDir).absoluteFilePath(filename);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "Failed to open crash report file:" << filePath;
        return false;
    }

    QTextStream out(&file);
    out << reportContent;
    file.close();

    // Also save a human-readable version
    QString readableFilename = QString("crash_%1_%2.txt")
        .arg(crashInfo.timestamp.toString("yyyyMMdd_hhmmss"))
        .arg(crashInfo.processId);

    QString readableFilePath = QDir(m_crashLogDir).absoluteFilePath(readableFilename);
    QFile readableFile(readableFilePath);
    if (readableFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream readableOut(&readableFile);
        readableOut << "CRASH REPORT\n";
        readableOut << "============\n\n";
        readableOut << "Timestamp: " << crashInfo.timestamp.toString() << "\n";
        readableOut << "Crash Type: " << crashInfo.crashType << "\n";
        readableOut << "Signal: " << crashInfo.signal << "\n";
        readableOut << "Process ID: " << crashInfo.processId << "\n";
        readableOut << "Thread ID: " << crashInfo.threadId << "\n\n";
        readableOut << crashInfo.stackTrace << "\n\n";
        readableOut << "System Information:\n" << crashInfo.systemInfo << "\n\n";
        readableOut << "Memory Information:\n" << crashInfo.memoryInfo << "\n\n";
        readableOut << "Build Information:\n" << crashInfo.buildInfo << "\n\n";
        readableOut << "User Actions:\n" << crashInfo.userActions << "\n";
        readableFile.close();
    }

    emit crashReportGenerated(filePath);

    Logger* logger = Logger::instance();
    logger->info(QString("Crash report saved to: %1").arg(filePath), "CrashHandler");

    return true;
}

QString ErrorHandler::getCrashLogDirectory()
{
    if (m_crashLogDir.isEmpty()) {
        QString appDataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        m_crashLogDir = QDir(appDataDir).absoluteFilePath("crash_logs");
    }
    return m_crashLogDir;
}

void ErrorHandler::ensureCrashLogDirectory()
{
    m_crashLogDir = getCrashLogDirectory();
    QDir dir;
    if (!dir.exists(m_crashLogDir)) {
        if (!dir.mkpath(m_crashLogDir)) {
            qDebug() << "Failed to create crash log directory:" << m_crashLogDir;
            // Fallback to temp directory
            m_crashLogDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
        }
    }
}

QString ErrorHandler::packageCrashLogs()
{
    if (!m_crashLogPackager) {
        return QString();
    }

    // Use the CrashLogPackager to create a comprehensive package
    QString packagePath = m_crashLogPackager->createQuickPackage(m_crashLogDir);

    if (!packagePath.isEmpty()) {
        Logger* logger = Logger::instance();
        logger->info(QString("Crash logs packaged successfully: %1").arg(packagePath), "ErrorHandler");
    }

    return packagePath;
}
