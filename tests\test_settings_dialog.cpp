#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QCheckBox>
#include <QComboBox>
#include <QSlider>
#include <QTabWidget>
#include <QPushButton>
#include <QApplication>

#include "SettingsDialog.h"
#include "ElaIntegration.h"

class TestSettingsDialog : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic functionality tests
    void testDialogCreation();
    void testTabsCreation();
    void testDefaultValues();
    void testResetToDefaults();

    // General settings tests
    void testDefaultZoomGetterSetter();
    void testAutoFitOnOpenGetterSetter();
    void testRememberWindowStateGetterSetter();

    // Performance settings tests
    void testCacheSizeGetterSetter();
    void testPreloadRangeGetterSetter();
    void testShowMemoryUsageGetterSetter();
    void testCacheSizeLabel();

    // Export settings tests
    void testExportFormatGetterSetter();
    void testExportQualityGetterSetter();
    void testExportQualityLabel();

    // Interface settings tests
    void testThemeModeGetterSetter();
    void testShowThumbnailsGetterSetter();

    // UI component tests
    void testUIComponentsExist();
    void testTabNavigation();
    void testButtonFunctionality();

    // Validation tests
    void testZoomRangeValidation();
    void testCacheSizeValidation();
    void testPreloadRangeValidation();
    void testExportQualityValidation();

    // Dialog behavior tests
    void testDialogAccept();
    void testDialogReject();
    void testDialogButtons();

    // Settings persistence tests
    void testSettingsRetrieval();
    void testSettingsApplication();

    // Theme integration tests
    void testThemeApplication();
    void testThemeChanges();

    // Edge cases and validation
    void testExtremeValues();
    void testInvalidInputs();
    void testUIStateConsistency();

    // Integration tests
    void testCompleteSettingsWorkflow();
    void testSettingsValidation();

private:
    SettingsDialog* m_settingsDialog;
    
    void verifyDefaultSettings();
    void setAllSettingsToNonDefaults();
    void verifyNonDefaultSettings();
    bool findTabByName(const QString& tabName);
    template<typename T>
    T* findWidgetInDialog(const QString& objectName = QString());
};

void TestSettingsDialog::initTestCase()
{
    // Initialize application if not already done
    if (!QApplication::instance()) {
        int argc = 0;
        char** argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestSettingsDialog::cleanupTestCase()
{
    // Cleanup handled by Qt
}

void TestSettingsDialog::init()
{
    // Create fresh settings dialog for each test
    m_settingsDialog = new SettingsDialog();
}

void TestSettingsDialog::cleanup()
{
    delete m_settingsDialog;
    m_settingsDialog = nullptr;
}

void TestSettingsDialog::testDialogCreation()
{
    // Test that dialog is created successfully
    QVERIFY(m_settingsDialog != nullptr);
    
    // Test dialog properties
    QVERIFY(!m_settingsDialog->windowTitle().isEmpty());
    QVERIFY(m_settingsDialog->size().width() > 0);
    QVERIFY(m_settingsDialog->size().height() > 0);
}

void TestSettingsDialog::testTabsCreation()
{
    // Find tab widget
    ElaTab* tabWidget = m_settingsDialog->findChild<ElaTab*>();
    QVERIFY(tabWidget != nullptr);
    
    // Verify tabs exist
    QVERIFY(tabWidget->count() >= 4); // General, Performance, Export, Interface
    
    // Verify tab names (implementation-specific)
    QVERIFY(findTabByName("General"));
    QVERIFY(findTabByName("Performance"));
    QVERIFY(findTabByName("Export"));
    QVERIFY(findTabByName("Interface"));
}

void TestSettingsDialog::testDefaultValues()
{
    verifyDefaultSettings();
}

void TestSettingsDialog::testResetToDefaults()
{
    // Change all settings to non-default values
    setAllSettingsToNonDefaults();
    verifyNonDefaultSettings();
    
    // Reset to defaults
    m_settingsDialog->resetToDefaults();
    
    // Verify defaults are restored
    verifyDefaultSettings();
}

void TestSettingsDialog::testDefaultZoomGetterSetter()
{
    // Test default value
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 1.0);
    
    // Test setting valid values
    m_settingsDialog->setDefaultZoom(1.5);
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 1.5);
    
    m_settingsDialog->setDefaultZoom(0.5);
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 0.5);
    
    m_settingsDialog->setDefaultZoom(2.0);
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 2.0);
}

void TestSettingsDialog::testAutoFitOnOpenGetterSetter()
{
    // Test default value
    QVERIFY(!m_settingsDialog->getAutoFitOnOpen()); // Should default to false
    
    // Test setting true
    m_settingsDialog->setAutoFitOnOpen(true);
    QVERIFY(m_settingsDialog->getAutoFitOnOpen());
    
    // Test setting false
    m_settingsDialog->setAutoFitOnOpen(false);
    QVERIFY(!m_settingsDialog->getAutoFitOnOpen());
}

void TestSettingsDialog::testRememberWindowStateGetterSetter()
{
    // Test default value
    QVERIFY(m_settingsDialog->getRememberWindowState()); // Should default to true
    
    // Test setting false
    m_settingsDialog->setRememberWindowState(false);
    QVERIFY(!m_settingsDialog->getRememberWindowState());
    
    // Test setting true
    m_settingsDialog->setRememberWindowState(true);
    QVERIFY(m_settingsDialog->getRememberWindowState());
}

void TestSettingsDialog::testCacheSizeGetterSetter()
{
    // Test default value
    QCOMPARE(m_settingsDialog->getCacheSize(), 450); // Default cache size
    
    // Test setting valid values
    m_settingsDialog->setCacheSize(100);
    QCOMPARE(m_settingsDialog->getCacheSize(), 100);
    
    m_settingsDialog->setCacheSize(1000);
    QCOMPARE(m_settingsDialog->getCacheSize(), 1000);
    
    m_settingsDialog->setCacheSize(256);
    QCOMPARE(m_settingsDialog->getCacheSize(), 256);
}

void TestSettingsDialog::testPreloadRangeGetterSetter()
{
    // Test default value
    QCOMPARE(m_settingsDialog->getPreloadRange(), 2); // Default preload range
    
    // Test setting valid values
    m_settingsDialog->setPreloadRange(1);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 1);
    
    m_settingsDialog->setPreloadRange(5);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 5);
    
    m_settingsDialog->setPreloadRange(10);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 10);
}

void TestSettingsDialog::testShowMemoryUsageGetterSetter()
{
    // Test default value
    QVERIFY(!m_settingsDialog->getShowMemoryUsage()); // Should default to false
    
    // Test setting true
    m_settingsDialog->setShowMemoryUsage(true);
    QVERIFY(m_settingsDialog->getShowMemoryUsage());
    
    // Test setting false
    m_settingsDialog->setShowMemoryUsage(false);
    QVERIFY(!m_settingsDialog->getShowMemoryUsage());
}

void TestSettingsDialog::testCacheSizeLabel()
{
    // Test that cache size label updates when cache size changes
    m_settingsDialog->setCacheSize(512);
    
    // Find cache size label (implementation-specific)
    ElaLabel* label = m_settingsDialog->findChild<ElaLabel*>();
    if (label) {
        // Verify label contains size information
        QVERIFY(label->text().contains("512") || label->text().contains("MB"));
    }
}

void TestSettingsDialog::testExportFormatGetterSetter()
{
    // Test default value
    QCOMPARE(m_settingsDialog->getExportFormat(), QString("PNG"));
    
    // Test setting different formats
    m_settingsDialog->setExportFormat("JPEG");
    QCOMPARE(m_settingsDialog->getExportFormat(), QString("JPEG"));
    
    m_settingsDialog->setExportFormat("PDF");
    QCOMPARE(m_settingsDialog->getExportFormat(), QString("PDF"));
    
    m_settingsDialog->setExportFormat("PNG");
    QCOMPARE(m_settingsDialog->getExportFormat(), QString("PNG"));
}

void TestSettingsDialog::testExportQualityGetterSetter()
{
    // Test default value
    QCOMPARE(m_settingsDialog->getExportQuality(), 90); // Default quality
    
    // Test setting valid values
    m_settingsDialog->setExportQuality(50);
    QCOMPARE(m_settingsDialog->getExportQuality(), 50);
    
    m_settingsDialog->setExportQuality(100);
    QCOMPARE(m_settingsDialog->getExportQuality(), 100);
    
    m_settingsDialog->setExportQuality(75);
    QCOMPARE(m_settingsDialog->getExportQuality(), 75);
}

void TestSettingsDialog::testExportQualityLabel()
{
    // Test that export quality label updates when quality changes
    m_settingsDialog->setExportQuality(85);
    
    // Find export quality label
    ElaLabel* label = m_settingsDialog->findChild<ElaLabel*>();
    if (label) {
        // Verify label shows quality information
        QVERIFY(label->text().contains("85") || label->text().contains("%"));
    }
}

void TestSettingsDialog::testThemeModeGetterSetter()
{
    // Test default value (Light theme)
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Light));
    
    // Test setting Dark theme
    m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Dark));
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Dark));
    
    // Test setting Light theme
    m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Light));
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Light));
}

void TestSettingsDialog::testShowThumbnailsGetterSetter()
{
    // Test default value
    QVERIFY(m_settingsDialog->getShowThumbnails()); // Should default to true
    
    // Test setting false
    m_settingsDialog->setShowThumbnails(false);
    QVERIFY(!m_settingsDialog->getShowThumbnails());
    
    // Test setting true
    m_settingsDialog->setShowThumbnails(true);
    QVERIFY(m_settingsDialog->getShowThumbnails());
}

void TestSettingsDialog::verifyDefaultSettings()
{
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 1.0);
    QVERIFY(!m_settingsDialog->getAutoFitOnOpen());
    QVERIFY(m_settingsDialog->getRememberWindowState());
    QCOMPARE(m_settingsDialog->getCacheSize(), 450);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 2);
    QVERIFY(!m_settingsDialog->getShowMemoryUsage());
    QCOMPARE(m_settingsDialog->getExportFormat(), QString("PNG"));
    QCOMPARE(m_settingsDialog->getExportQuality(), 90);
    QVERIFY(m_settingsDialog->getShowThumbnails());
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Light));
}

void TestSettingsDialog::setAllSettingsToNonDefaults()
{
    m_settingsDialog->setDefaultZoom(2.5);
    m_settingsDialog->setAutoFitOnOpen(true);
    m_settingsDialog->setRememberWindowState(false);
    m_settingsDialog->setCacheSize(1024);
    m_settingsDialog->setPreloadRange(10);
    m_settingsDialog->setShowMemoryUsage(true);
    m_settingsDialog->setExportFormat("JPEG");
    m_settingsDialog->setExportQuality(50);
    m_settingsDialog->setShowThumbnails(false);
    m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Dark));
}

void TestSettingsDialog::verifyNonDefaultSettings()
{
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 2.5);
    QVERIFY(m_settingsDialog->getAutoFitOnOpen());
    QVERIFY(!m_settingsDialog->getRememberWindowState());
    QCOMPARE(m_settingsDialog->getCacheSize(), 1024);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 10);
    QVERIFY(m_settingsDialog->getShowMemoryUsage());
    QCOMPARE(m_settingsDialog->getExportFormat(), QString("JPEG"));
    QCOMPARE(m_settingsDialog->getExportQuality(), 50);
    QVERIFY(!m_settingsDialog->getShowThumbnails());
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Dark));
}

bool TestSettingsDialog::findTabByName(const QString& tabName)
{
    ElaTab* tabWidget = m_settingsDialog->findChild<ElaTab*>();
    if (!tabWidget) return false;
    
    for (int i = 0; i < tabWidget->count(); ++i) {
        if (tabWidget->tabText(i).contains(tabName, Qt::CaseInsensitive)) {
            return true;
        }
    }
    return false;
}

template<typename T>
T* TestSettingsDialog::findWidgetInDialog(const QString& objectName)
{
    if (objectName.isEmpty()) {
        return m_settingsDialog->findChild<T*>();
    } else {
        return m_settingsDialog->findChild<T*>(objectName);
    }
}

void TestSettingsDialog::testUIComponentsExist()
{
    // Test that essential UI components exist
    ElaTab* tabWidget = m_settingsDialog->findChild<ElaTab*>();
    QVERIFY(tabWidget != nullptr);

    // Test that spin boxes exist
    ElaDoubleSpin* zoomSpinBox = m_settingsDialog->findChild<ElaDoubleSpin*>();
    QVERIFY(zoomSpinBox != nullptr);

    ElaSpin* cacheSpinBox = m_settingsDialog->findChild<ElaSpin*>();
    QVERIFY(cacheSpinBox != nullptr);

    // Test that checkboxes exist
    QList<ElaCheck*> checkboxes = m_settingsDialog->findChildren<ElaCheck*>();
    QVERIFY(checkboxes.size() >= 4); // At least 4 checkboxes

    // Test that combo boxes exist
    QList<ElaCombo*> comboBoxes = m_settingsDialog->findChildren<ElaCombo*>();
    QVERIFY(comboBoxes.size() >= 2); // At least 2 combo boxes

    // Test that reset button exists
    ElaButton* resetButton = m_settingsDialog->findChild<ElaButton*>();
    QVERIFY(resetButton != nullptr);
}

void TestSettingsDialog::testTabNavigation()
{
    ElaTab* tabWidget = m_settingsDialog->findChild<ElaTab*>();
    QVERIFY(tabWidget != nullptr);

    int tabCount = tabWidget->count();
    QVERIFY(tabCount >= 4);

    // Test switching between tabs
    for (int i = 0; i < tabCount; ++i) {
        tabWidget->setCurrentIndex(i);
        QCOMPARE(tabWidget->currentIndex(), i);

        // Verify tab content is accessible
        QWidget* currentTab = tabWidget->currentWidget();
        QVERIFY(currentTab != nullptr);
        QVERIFY(currentTab->isVisible());
    }
}

void TestSettingsDialog::testButtonFunctionality()
{
    // Find reset button
    ElaButton* resetButton = m_settingsDialog->findChild<ElaButton*>();
    QVERIFY(resetButton != nullptr);

    // Change settings to non-defaults
    setAllSettingsToNonDefaults();

    // Click reset button
    QTest::mouseClick(resetButton, Qt::LeftButton);

    // Verify settings were reset
    verifyDefaultSettings();
}

void TestSettingsDialog::testZoomRangeValidation()
{
    // Test minimum zoom
    m_settingsDialog->setDefaultZoom(0.1);
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 0.1);

    // Test maximum zoom
    m_settingsDialog->setDefaultZoom(5.0);
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 5.0);

    // Test values within range
    m_settingsDialog->setDefaultZoom(1.5);
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 1.5);
}

void TestSettingsDialog::testCacheSizeValidation()
{
    // Test various cache sizes
    m_settingsDialog->setCacheSize(64);
    QCOMPARE(m_settingsDialog->getCacheSize(), 64);

    m_settingsDialog->setCacheSize(2048);
    QCOMPARE(m_settingsDialog->getCacheSize(), 2048);

    // Test that cache size is properly constrained by the spin box
    ElaSpin* cacheSpinBox = m_settingsDialog->findChild<ElaSpin*>();
    if (cacheSpinBox) {
        int minValue = cacheSpinBox->minimum();
        int maxValue = cacheSpinBox->maximum();

        m_settingsDialog->setCacheSize(minValue);
        QCOMPARE(m_settingsDialog->getCacheSize(), minValue);

        m_settingsDialog->setCacheSize(maxValue);
        QCOMPARE(m_settingsDialog->getCacheSize(), maxValue);
    }
}

void TestSettingsDialog::testPreloadRangeValidation()
{
    // Test various preload ranges
    m_settingsDialog->setPreloadRange(1);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 1);

    m_settingsDialog->setPreloadRange(20);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 20);

    // Test reasonable bounds
    m_settingsDialog->setPreloadRange(0);
    QVERIFY(m_settingsDialog->getPreloadRange() >= 0);
}

void TestSettingsDialog::testExportQualityValidation()
{
    // Test quality range (typically 1-100)
    m_settingsDialog->setExportQuality(1);
    QCOMPARE(m_settingsDialog->getExportQuality(), 1);

    m_settingsDialog->setExportQuality(100);
    QCOMPARE(m_settingsDialog->getExportQuality(), 100);

    m_settingsDialog->setExportQuality(50);
    QCOMPARE(m_settingsDialog->getExportQuality(), 50);
}

void TestSettingsDialog::testDialogAccept()
{
    QSignalSpy acceptSpy(m_settingsDialog, &QDialog::accepted);

    // Simulate clicking OK button (right button in ElaContentDialog)
    m_settingsDialog->accept();

    QCOMPARE(acceptSpy.count(), 1);
}

void TestSettingsDialog::testDialogReject()
{
    QSignalSpy rejectSpy(m_settingsDialog, &QDialog::rejected);

    // Simulate clicking Cancel button (left button in ElaContentDialog)
    m_settingsDialog->reject();

    QCOMPARE(rejectSpy.count(), 1);
}

void TestSettingsDialog::testDialogButtons()
{
    // Test that dialog has proper button configuration
    // This is implementation-specific for ElaContentDialog
    QVERIFY(true); // Placeholder - actual button testing would require ElaContentDialog specifics
}

void TestSettingsDialog::testSettingsRetrieval()
{
    // Set specific values
    m_settingsDialog->setDefaultZoom(1.25);
    m_settingsDialog->setCacheSize(512);
    m_settingsDialog->setExportFormat("JPEG");
    m_settingsDialog->setExportQuality(85);
    m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Dark));

    // Retrieve and verify values
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 1.25);
    QCOMPARE(m_settingsDialog->getCacheSize(), 512);
    QCOMPARE(m_settingsDialog->getExportFormat(), QString("JPEG"));
    QCOMPARE(m_settingsDialog->getExportQuality(), 85);
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Dark));
}

void TestSettingsDialog::testSettingsApplication()
{
    // Test that settings can be applied consistently
    double testZoom = 1.75;
    int testCache = 768;
    bool testMemoryUsage = true;

    m_settingsDialog->setDefaultZoom(testZoom);
    m_settingsDialog->setCacheSize(testCache);
    m_settingsDialog->setShowMemoryUsage(testMemoryUsage);

    // Verify settings are applied
    QCOMPARE(m_settingsDialog->getDefaultZoom(), testZoom);
    QCOMPARE(m_settingsDialog->getCacheSize(), testCache);
    QCOMPARE(m_settingsDialog->getShowMemoryUsage(), testMemoryUsage);
}

void TestSettingsDialog::testThemeApplication()
{
    // Test theme mode changes
    m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Light));
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Light));

    m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Dark));
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Dark));
}

void TestSettingsDialog::testThemeChanges()
{
    // Test that theme combo box properly reflects changes
    ElaCombo* themeCombo = nullptr;
    QList<ElaCombo*> comboBoxes = m_settingsDialog->findChildren<ElaCombo*>();

    // Find theme combo box (implementation-specific)
    for (ElaCombo* combo : comboBoxes) {
        if (combo->count() >= 2) { // Theme combo should have at least Light/Dark
            themeCombo = combo;
            break;
        }
    }

    if (themeCombo) {
        // Test that setting theme mode updates combo box
        m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Dark));

        // Verify combo box selection
        bool foundDarkTheme = false;
        for (int i = 0; i < themeCombo->count(); ++i) {
            if (themeCombo->itemData(i).toInt() == static_cast<int>(ElaThemeType::Dark)) {
                QCOMPARE(themeCombo->currentIndex(), i);
                foundDarkTheme = true;
                break;
            }
        }
        QVERIFY(foundDarkTheme);
    }
}

void TestSettingsDialog::testExtremeValues()
{
    // Test with extreme but valid values
    m_settingsDialog->setDefaultZoom(0.1); // Minimum zoom
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 0.1);

    m_settingsDialog->setDefaultZoom(5.0); // Maximum zoom
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 5.0);

    // Test large cache size
    m_settingsDialog->setCacheSize(4096);
    QCOMPARE(m_settingsDialog->getCacheSize(), 4096);

    // Test extreme preload range
    m_settingsDialog->setPreloadRange(50);
    QCOMPARE(m_settingsDialog->getPreloadRange(), 50);
}

void TestSettingsDialog::testInvalidInputs()
{
    // Test that invalid theme mode is handled gracefully
    m_settingsDialog->setThemeMode(999); // Invalid theme mode

    // Should not crash and should maintain a valid state
    int currentTheme = m_settingsDialog->getThemeMode();
    QVERIFY(currentTheme == static_cast<int>(ElaThemeType::Light) ||
            currentTheme == static_cast<int>(ElaThemeType::Dark));

    // Test invalid export format
    m_settingsDialog->setExportFormat("INVALID_FORMAT");

    // Should handle gracefully (may or may not accept invalid format)
    QVERIFY(true); // Test passes if no crash occurs
}

void TestSettingsDialog::testUIStateConsistency()
{
    // Test that UI state remains consistent after multiple changes
    for (int i = 0; i < 10; ++i) {
        m_settingsDialog->setDefaultZoom(1.0 + i * 0.1);
        m_settingsDialog->setCacheSize(100 + i * 50);
        m_settingsDialog->setPreloadRange(1 + i);

        // Verify values are consistent
        QCOMPARE(m_settingsDialog->getDefaultZoom(), 1.0 + i * 0.1);
        QCOMPARE(m_settingsDialog->getCacheSize(), 100 + i * 50);
        QCOMPARE(m_settingsDialog->getPreloadRange(), 1 + i);
    }
}

void TestSettingsDialog::testCompleteSettingsWorkflow()
{
    // Test complete workflow: change settings, reset, change again

    // Step 1: Change all settings
    setAllSettingsToNonDefaults();
    verifyNonDefaultSettings();

    // Step 2: Reset to defaults
    m_settingsDialog->resetToDefaults();
    verifyDefaultSettings();

    // Step 3: Change settings again
    m_settingsDialog->setDefaultZoom(1.8);
    m_settingsDialog->setCacheSize(800);
    m_settingsDialog->setExportQuality(75);
    m_settingsDialog->setThemeMode(static_cast<int>(ElaThemeType::Dark));

    // Step 4: Verify final state
    QCOMPARE(m_settingsDialog->getDefaultZoom(), 1.8);
    QCOMPARE(m_settingsDialog->getCacheSize(), 800);
    QCOMPARE(m_settingsDialog->getExportQuality(), 75);
    QCOMPARE(m_settingsDialog->getThemeMode(), static_cast<int>(ElaThemeType::Dark));
}

void TestSettingsDialog::testSettingsValidation()
{
    // Test that all settings maintain valid states

    // Set various combinations of settings
    QList<double> zoomValues = {0.1, 0.5, 1.0, 1.5, 2.0, 5.0};
    QList<int> cacheSizes = {64, 128, 256, 512, 1024, 2048};
    QList<int> preloadRanges = {1, 2, 5, 10, 20};
    QList<int> exportQualities = {10, 25, 50, 75, 90, 100};

    for (double zoom : zoomValues) {
        m_settingsDialog->setDefaultZoom(zoom);
        QCOMPARE(m_settingsDialog->getDefaultZoom(), zoom);
    }

    for (int cache : cacheSizes) {
        m_settingsDialog->setCacheSize(cache);
        QCOMPARE(m_settingsDialog->getCacheSize(), cache);
    }

    for (int preload : preloadRanges) {
        m_settingsDialog->setPreloadRange(preload);
        QCOMPARE(m_settingsDialog->getPreloadRange(), preload);
    }

    for (int quality : exportQualities) {
        m_settingsDialog->setExportQuality(quality);
        QCOMPARE(m_settingsDialog->getExportQuality(), quality);
    }
}

QTEST_MAIN(TestSettingsDialog)
#include "test_settings_dialog.moc"
