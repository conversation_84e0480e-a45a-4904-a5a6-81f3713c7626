#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QFile>
#include <QTextStream>
#include <QThread>
#include <QTimer>
#include <QEventLoop>

#include "Logger.h"

class TestLogger : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Singleton tests
    void testSingletonInstance();
    void testSingletonThreadSafety();

    // Basic logging tests
    void testLogLevels();
    void testLogLevelFiltering();
    void testLogMessageFormatting();
    void testLogCategories();
    void testLogSignalEmission();

    // Configuration tests
    void testLogToFileConfiguration();
    void testLogToConsoleConfiguration();
    void testLogFilePathConfiguration();
    void testMaxLogFileSizeConfiguration();

    // File operations tests
    void testLogFileCreation();
    void testLogFileWriting();
    void testLogFileRotation();
    void testLogFileRotationWithBackup();
    void testLogFilePermissions();
    void testLogFileCorruption();

    // Performance timer tests
    void testPerformanceTimerBasic();
    void testPerformanceTimerMultiple();
    void testPerformanceTimerNested();
    void testPerformanceTimerInvalidOperations();
    void testPerformanceTimerThreadSafety();

    // System info tests
    void testSystemInfoLogging();
    void testExceptionLogging();

    // Edge cases and error handling
    void testInvalidLogFilePath();
    void testDiskFullScenario();
    void testConcurrentLogging();
    void testLogRotationDuringWrite();
    void testMemoryUsage();

    // Macro tests
    void testLoggingMacros();
    void testTimerMacros();

private:
    QTemporaryDir* m_tempDir;
    QString m_testLogPath;
    Logger* m_logger;
    
    void createLargeLogFile(const QString& path, qint64 size);
    void simulateDiskFull();
    void restoreDiskSpace();
    bool isLogMessageValid(const QString& message, LogLevel level, const QString& content);
};

void TestLogger::initTestCase()
{
    // Create temporary directory for test files
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    m_testLogPath = m_tempDir->path() + "/test.log";
    
    // Get logger instance
    m_logger = Logger::instance();
    QVERIFY(m_logger != nullptr);
}

void TestLogger::cleanupTestCase()
{
    delete m_tempDir;
    m_tempDir = nullptr;
}

void TestLogger::init()
{
    // Reset logger configuration before each test
    m_logger->setLogLevel(LogLevel::Debug);
    m_logger->setLogToFile(true);
    m_logger->setLogToConsole(false); // Reduce test output noise
    m_logger->setLogFilePath(m_testLogPath);
    m_logger->setMaxLogFileSize(1024 * 1024); // 1MB for tests
    
    // Clean up any existing log files
    QFile::remove(m_testLogPath);
    QFile::remove(m_testLogPath + ".old");
}

void TestLogger::cleanup()
{
    // Clean up test files
    QFile::remove(m_testLogPath);
    QFile::remove(m_testLogPath + ".old");
}

void TestLogger::testSingletonInstance()
{
    Logger* logger1 = Logger::instance();
    Logger* logger2 = Logger::instance();
    
    QVERIFY(logger1 != nullptr);
    QVERIFY(logger2 != nullptr);
    QCOMPARE(logger1, logger2); // Should be the same instance
    QCOMPARE(logger1, m_logger); // Should be our test instance
}

void TestLogger::testSingletonThreadSafety()
{
    QList<Logger*> instances;
    QMutex instancesMutex;
    
    // Create multiple threads that try to get the singleton instance
    QList<QThread*> threads;
    for (int i = 0; i < 10; ++i) {
        QThread* thread = QThread::create([&instances, &instancesMutex]() {
            Logger* instance = Logger::instance();
            QMutexLocker locker(&instancesMutex);
            instances.append(instance);
        });
        threads.append(thread);
        thread->start();
    }
    
    // Wait for all threads to complete
    for (QThread* thread : threads) {
        thread->wait();
        delete thread;
    }
    
    // All instances should be the same
    QCOMPARE(instances.size(), 10);
    for (Logger* instance : instances) {
        QCOMPARE(instance, m_logger);
    }
}

void TestLogger::testLogLevels()
{
    // Test all log levels
    m_logger->setLogLevel(LogLevel::Debug);
    
    m_logger->debug("Debug message", "Test");
    m_logger->info("Info message", "Test");
    m_logger->warning("Warning message", "Test");
    m_logger->error("Error message", "Test");
    m_logger->critical("Critical message", "Test");
    
    // Check that all messages were written to file
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();
    
    QVERIFY(content.contains("Debug message"));
    QVERIFY(content.contains("Info message"));
    QVERIFY(content.contains("Warning message"));
    QVERIFY(content.contains("Error message"));
    QVERIFY(content.contains("Critical message"));
    
    // Verify log level strings are correct
    QVERIFY(content.contains("[DEBUG]"));
    QVERIFY(content.contains("[INFO ]"));
    QVERIFY(content.contains("[WARN ]"));
    QVERIFY(content.contains("[ERROR]"));
    QVERIFY(content.contains("[CRIT ]"));
}

void TestLogger::testLogLevelFiltering()
{
    // Set log level to Warning - should filter out Debug and Info
    m_logger->setLogLevel(LogLevel::Warning);
    
    m_logger->debug("Debug message", "Test");
    m_logger->info("Info message", "Test");
    m_logger->warning("Warning message", "Test");
    m_logger->error("Error message", "Test");
    m_logger->critical("Critical message", "Test");
    
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();
    
    // Debug and Info should be filtered out
    QVERIFY(!content.contains("Debug message"));
    QVERIFY(!content.contains("Info message"));
    
    // Warning, Error, and Critical should be present
    QVERIFY(content.contains("Warning message"));
    QVERIFY(content.contains("Error message"));
    QVERIFY(content.contains("Critical message"));
    
    // Test Critical level - only critical messages should pass
    QFile::remove(m_testLogPath);
    m_logger->setLogLevel(LogLevel::Critical);
    
    m_logger->debug("Debug message 2", "Test");
    m_logger->info("Info message 2", "Test");
    m_logger->warning("Warning message 2", "Test");
    m_logger->error("Error message 2", "Test");
    m_logger->critical("Critical message 2", "Test");
    
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    content = logFile.readAll();
    logFile.close();
    
    QVERIFY(!content.contains("Debug message 2"));
    QVERIFY(!content.contains("Info message 2"));
    QVERIFY(!content.contains("Warning message 2"));
    QVERIFY(!content.contains("Error message 2"));
    QVERIFY(content.contains("Critical message 2"));
}

void TestLogger::testLogMessageFormatting()
{
    m_logger->info("Test message", "TestCategory");
    
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();
    
    // Check format: [timestamp] [level] [Thread:id] [category] message
    QStringList lines = content.split('\n', Qt::SkipEmptyParts);
    QVERIFY(!lines.isEmpty());
    
    QString logLine = lines.last();
    QVERIFY(logLine.contains("[INFO ]"));
    QVERIFY(logLine.contains("[TestCategory]"));
    QVERIFY(logLine.contains("Test message"));
    QVERIFY(logLine.contains("[Thread:"));
    
    // Check timestamp format (YYYY-MM-DD HH:MM:SS.ZZZ)
    QRegularExpression timestampRegex(R"(\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\])");
    QVERIFY(timestampRegex.match(logLine).hasMatch());
}

void TestLogger::testLogCategories()
{
    m_logger->info("Message without category");
    m_logger->info("Message with category", "TestCategory");
    m_logger->info("Message with empty category", "");
    
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();
    
    QStringList lines = content.split('\n', Qt::SkipEmptyParts);
    QVERIFY(lines.size() >= 3);
    
    // Message without category should not have category brackets
    QString line1 = lines[lines.size() - 3];
    QVERIFY(!line1.contains("[TestCategory]"));
    QVERIFY(line1.contains("Message without category"));
    
    // Message with category should have category brackets
    QString line2 = lines[lines.size() - 2];
    QVERIFY(line2.contains("[TestCategory]"));
    QVERIFY(line2.contains("Message with category"));
    
    // Message with empty category should not have category brackets
    QString line3 = lines[lines.size() - 1];
    QVERIFY(line3.contains("Message with empty category"));
}

void TestLogger::testLogSignalEmission()
{
    QSignalSpy spy(m_logger, &Logger::logMessage);
    
    m_logger->info("Test signal emission", "SignalTest");
    
    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).value<LogLevel>(), LogLevel::Info);
    QCOMPARE(arguments.at(1).toString(), QString("Test signal emission"));
    QCOMPARE(arguments.at(2).toString(), QString("SignalTest"));
}

void TestLogger::testLogToFileConfiguration()
{
    // Test enabling/disabling file logging
    m_logger->setLogToFile(false);
    QCOMPARE(m_logger->getLogToFile(), false);

    m_logger->info("Should not be in file", "Test");

    // File should not exist or be empty
    QFile logFile(m_testLogPath);
    QVERIFY(!logFile.exists() || logFile.size() == 0);

    // Re-enable file logging
    m_logger->setLogToFile(true);
    QCOMPARE(m_logger->getLogToFile(), true);

    m_logger->info("Should be in file", "Test");

    QVERIFY(logFile.exists());
    QVERIFY(logFile.size() > 0);
}

void TestLogger::testLogToConsoleConfiguration()
{
    // Test console logging configuration
    m_logger->setLogToConsole(true);
    QCOMPARE(m_logger->getLogToConsole(), true);

    m_logger->setLogToConsole(false);
    QCOMPARE(m_logger->getLogToConsole(), false);
}

void TestLogger::testLogFilePathConfiguration()
{
    QString newPath = m_tempDir->path() + "/custom.log";
    m_logger->setLogFilePath(newPath);
    QCOMPARE(m_logger->getLogFilePath(), newPath);

    m_logger->info("Test custom path", "Test");

    QFile customFile(newPath);
    QVERIFY(customFile.exists());
    QVERIFY(customFile.open(QIODevice::ReadOnly));
    QString content = customFile.readAll();
    customFile.close();

    QVERIFY(content.contains("Test custom path"));
}

void TestLogger::testMaxLogFileSizeConfiguration()
{
    qint64 customSize = 2048; // 2KB
    m_logger->setMaxLogFileSize(customSize);
    QCOMPARE(m_logger->getMaxLogFileSize(), customSize);
}

void TestLogger::testLogFileCreation()
{
    // Remove existing file
    QFile::remove(m_testLogPath);

    // Log a message - should create the file
    m_logger->info("File creation test", "Test");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.exists());
    QVERIFY(logFile.size() > 0);
}

void TestLogger::testLogFileWriting()
{
    QString testMessage = "Multi-line test message\nwith special characters: äöü@#$%";
    m_logger->info(testMessage, "WriteTest");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Multi-line test message"));
    QVERIFY(content.contains("with special characters: äöü@#$%"));
}

void TestLogger::testLogFileRotation()
{
    // Set small max file size to trigger rotation
    m_logger->setMaxLogFileSize(100); // 100 bytes

    // Write enough data to trigger rotation
    for (int i = 0; i < 10; ++i) {
        m_logger->info(QString("Long message to trigger rotation %1").arg(i), "RotationTest");
    }

    // Check that backup file was created
    QFile backupFile(m_testLogPath + ".old");
    QVERIFY(backupFile.exists());

    // Check that new log file exists and contains recent messages
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.exists());
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Log file rotated"));
}

void TestLogger::testLogFileRotationWithBackup()
{
    // Create initial log file
    m_logger->info("Initial message", "Test");

    // Set small size and trigger rotation
    m_logger->setMaxLogFileSize(50);
    m_logger->info("Message that should trigger rotation", "Test");

    // Verify backup exists and contains initial message
    QFile backupFile(m_testLogPath + ".old");
    QVERIFY(backupFile.exists());
    QVERIFY(backupFile.open(QIODevice::ReadOnly));
    QString backupContent = backupFile.readAll();
    backupFile.close();

    QVERIFY(backupContent.contains("Initial message"));
}

void TestLogger::testLogFilePermissions()
{
    // Test logging to a directory without write permissions
    QString restrictedPath = "/root/restricted.log"; // Typically no write access
    m_logger->setLogFilePath(restrictedPath);

    // This should not crash, but may not create the file
    m_logger->info("Permission test", "Test");

    // Reset to valid path
    m_logger->setLogFilePath(m_testLogPath);
    m_logger->info("Back to normal", "Test");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.exists());
}

void TestLogger::testLogFileCorruption()
{
    // Create a corrupted log file (directory instead of file)
    QDir().mkdir(m_testLogPath);

    // This should handle the error gracefully
    m_logger->info("Corruption test", "Test");

    // Clean up and reset
    QDir().rmdir(m_testLogPath);
    m_logger->info("After cleanup", "Test");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.exists());
}

void TestLogger::testPerformanceTimerBasic()
{
    QSignalSpy spy(m_logger, &Logger::logMessage);

    m_logger->startTimer("TestOperation");
    QThread::msleep(50); // Wait 50ms
    m_logger->endTimer("TestOperation");

    // Should have logged start and end messages
    QVERIFY(spy.count() >= 2);

    // Check for timer messages in log file
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Timer started: TestOperation"));
    QVERIFY(content.contains("Timer ended: TestOperation"));
    QVERIFY(content.contains("ms)")); // Should contain elapsed time
}

void TestLogger::testPerformanceTimerMultiple()
{
    m_logger->startTimer("Operation1");
    m_logger->startTimer("Operation2");

    QThread::msleep(30);

    m_logger->endTimer("Operation1");
    m_logger->endTimer("Operation2");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Timer started: Operation1"));
    QVERIFY(content.contains("Timer started: Operation2"));
    QVERIFY(content.contains("Timer ended: Operation1"));
    QVERIFY(content.contains("Timer ended: Operation2"));
}

void TestLogger::testPerformanceTimerNested()
{
    m_logger->startTimer("OuterOperation");
    m_logger->startTimer("InnerOperation");

    QThread::msleep(20);

    m_logger->endTimer("InnerOperation");
    m_logger->endTimer("OuterOperation");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("OuterOperation"));
    QVERIFY(content.contains("InnerOperation"));
}

void TestLogger::testPerformanceTimerInvalidOperations()
{
    // Try to end a timer that was never started
    m_logger->endTimer("NonExistentTimer");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Timer not found: NonExistentTimer"));
}

void TestLogger::testPerformanceTimerThreadSafety()
{
    QList<QThread*> threads;

    // Create multiple threads that start and end timers
    for (int i = 0; i < 5; ++i) {
        QThread* thread = QThread::create([this, i]() {
            QString timerName = QString("ThreadTimer%1").arg(i);
            m_logger->startTimer(timerName);
            QThread::msleep(10);
            m_logger->endTimer(timerName);
        });
        threads.append(thread);
        thread->start();
    }

    // Wait for all threads to complete
    for (QThread* thread : threads) {
        thread->wait();
        delete thread;
    }

    // Verify all timers were logged
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    for (int i = 0; i < 5; ++i) {
        QString timerName = QString("ThreadTimer%1").arg(i);
        QVERIFY(content.contains(QString("Timer started: %1").arg(timerName)));
        QVERIFY(content.contains(QString("Timer ended: %1").arg(timerName)));
    }
}

void TestLogger::testSystemInfoLogging()
{
    m_logger->logSystemInfo();

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Operating System:"));
    QVERIFY(content.contains("Qt Version:"));
    QVERIFY(content.contains("Application Version:"));
    QVERIFY(content.contains("Build Architecture:"));
}

void TestLogger::testExceptionLogging()
{
    QString exceptionMsg = "Test exception occurred";
    QString location = "TestLogger::testExceptionLogging";

    m_logger->logException(exceptionMsg, location);

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("[CRIT ]"));
    QVERIFY(content.contains("Exception in TestLogger::testExceptionLogging"));
    QVERIFY(content.contains("Test exception occurred"));
}

void TestLogger::testInvalidLogFilePath()
{
    // Test with invalid characters in path
    QString invalidPath = "invalid<>path|?.log";
    m_logger->setLogFilePath(invalidPath);

    // Should handle gracefully without crashing
    m_logger->info("Test invalid path", "Test");

    // Reset to valid path
    m_logger->setLogFilePath(m_testLogPath);
    QVERIFY(true); // If we get here, no crash occurred
}

void TestLogger::testDiskFullScenario()
{
    // This is difficult to test reliably, so we'll simulate by setting a very small max size
    m_logger->setMaxLogFileSize(1); // 1 byte - will trigger rotation immediately

    // Try to log multiple messages
    for (int i = 0; i < 5; ++i) {
        m_logger->info(QString("Message %1").arg(i), "DiskFullTest");
    }

    // Should handle gracefully without crashing
    QVERIFY(true);
}

void TestLogger::testConcurrentLogging()
{
    QList<QThread*> threads;

    // Create multiple threads that log simultaneously
    for (int i = 0; i < 10; ++i) {
        QThread* thread = QThread::create([this, i]() {
            for (int j = 0; j < 10; ++j) {
                m_logger->info(QString("Thread %1 Message %2").arg(i).arg(j), "ConcurrentTest");
            }
        });
        threads.append(thread);
        thread->start();
    }

    // Wait for all threads to complete
    for (QThread* thread : threads) {
        thread->wait();
        delete thread;
    }

    // Verify all messages were logged
    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    // Count total messages (should be 100)
    int messageCount = content.count("ConcurrentTest");
    QCOMPARE(messageCount, 100);
}

void TestLogger::testLogRotationDuringWrite()
{
    // Set small max size
    m_logger->setMaxLogFileSize(200);

    // Start multiple threads that write simultaneously
    QList<QThread*> threads;
    for (int i = 0; i < 3; ++i) {
        QThread* thread = QThread::create([this, i]() {
            for (int j = 0; j < 20; ++j) {
                m_logger->info(QString("Rotation test thread %1 message %2 with extra content").arg(i).arg(j), "RotationTest");
                QThread::msleep(1);
            }
        });
        threads.append(thread);
        thread->start();
    }

    for (QThread* thread : threads) {
        thread->wait();
        delete thread;
    }

    // Should complete without crashing
    QVERIFY(QFile::exists(m_testLogPath));
}

void TestLogger::testMemoryUsage()
{
    // Log many messages and ensure memory doesn't grow excessively
    for (int i = 0; i < 1000; ++i) {
        m_logger->info(QString("Memory test message %1").arg(i), "MemoryTest");
    }

    // This is mainly a crash test - if we get here, memory management is working
    QVERIFY(true);
}

void TestLogger::testLoggingMacros()
{
    // Test convenience macros
    LOG_DEBUG("Debug macro test");
    LOG_INFO("Info macro test");
    LOG_WARNING("Warning macro test");
    LOG_ERROR("Error macro test");
    LOG_CRITICAL("Critical macro test");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Debug macro test"));
    QVERIFY(content.contains("Info macro test"));
    QVERIFY(content.contains("Warning macro test"));
    QVERIFY(content.contains("Error macro test"));
    QVERIFY(content.contains("Critical macro test"));
}

void TestLogger::testTimerMacros()
{
    LOG_TIMER_START("MacroTimer");
    QThread::msleep(10);
    LOG_TIMER_END("MacroTimer");

    QFile logFile(m_testLogPath);
    QVERIFY(logFile.open(QIODevice::ReadOnly));
    QString content = logFile.readAll();
    logFile.close();

    QVERIFY(content.contains("Timer started: MacroTimer"));
    QVERIFY(content.contains("Timer ended: MacroTimer"));
}

void TestLogger::createLargeLogFile(const QString& path, qint64 size)
{
    QFile file(path);
    if (file.open(QIODevice::WriteOnly)) {
        QByteArray data(1024, 'X'); // 1KB of X's
        qint64 written = 0;
        while (written < size) {
            qint64 toWrite = qMin(static_cast<qint64>(data.size()), size - written);
            file.write(data.left(toWrite));
            written += toWrite;
        }
        file.close();
    }
}

void TestLogger::simulateDiskFull()
{
    // This is a placeholder - actual disk full simulation is complex
    // In a real test environment, you might use filesystem quotas or mock objects
}

void TestLogger::restoreDiskSpace()
{
    // Placeholder for restoring disk space after simulation
}

bool TestLogger::isLogMessageValid(const QString& message, LogLevel level, const QString& content)
{
    // Check if log message has correct format and content
    return message.contains(content) &&
           message.contains(QRegularExpression(R"(\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\])"));
}

QTEST_MAIN(TestLogger)
#include "test_logger.moc"
