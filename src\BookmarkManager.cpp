#include "BookmarkManager.h"
#include <QUuid>
#include <QFileInfo>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <algorithm>

const QString BookmarkManager::SETTINGS_GROUP = "EnhancedBookmarks";
const QString BookmarkManager::BOOKMARKS_KEY = "bookmarks";

// EnhancedBookmark implementation
QJsonObject EnhancedBookmark::toJson() const
{
    QJsonObject obj;
    obj["id"] = id;
    obj["name"] = name;
    obj["description"] = description;
    obj["filePath"] = filePath;
    obj["fileName"] = fileName;
    obj["pageNumber"] = pageNumber;
    obj["zoomFactor"] = zoomFactor;
    obj["rotation"] = rotation;
    obj["created"] = created.toString(Qt::ISODate);
    obj["lastAccessed"] = lastAccessed.toString(Qt::ISODate);
    obj["accessCount"] = accessCount;
    obj["tags"] = QJsonArray::fromStringList(tags);
    obj["color"] = color.name();
    obj["notes"] = notes;
    obj["isFavorite"] = isFavorite;
    obj["category"] = category;
    
    // Viewport rect
    QJsonObject viewportObj;
    viewportObj["x"] = viewportRect.x();
    viewportObj["y"] = viewportRect.y();
    viewportObj["width"] = viewportRect.width();
    viewportObj["height"] = viewportRect.height();
    obj["viewportRect"] = viewportObj;
    
    return obj;
}

EnhancedBookmark EnhancedBookmark::fromJson(const QJsonObject& json)
{
    EnhancedBookmark bookmark;
    bookmark.id = json["id"].toString();
    bookmark.name = json["name"].toString();
    bookmark.description = json["description"].toString();
    bookmark.filePath = json["filePath"].toString();
    bookmark.fileName = json["fileName"].toString();
    bookmark.pageNumber = json["pageNumber"].toInt();
    bookmark.zoomFactor = json["zoomFactor"].toDouble();
    bookmark.rotation = json["rotation"].toInt();
    bookmark.created = QDateTime::fromString(json["created"].toString(), Qt::ISODate);
    bookmark.lastAccessed = QDateTime::fromString(json["lastAccessed"].toString(), Qt::ISODate);
    bookmark.accessCount = json["accessCount"].toInt();
    
    // Tags
    QJsonArray tagsArray = json["tags"].toArray();
    for (const auto& value : tagsArray) {
        bookmark.tags.append(value.toString());
    }
    
    bookmark.color = QColor(json["color"].toString());
    bookmark.notes = json["notes"].toString();
    bookmark.isFavorite = json["isFavorite"].toBool();
    bookmark.category = json["category"].toString();
    
    // Viewport rect
    QJsonObject viewportObj = json["viewportRect"].toObject();
    bookmark.viewportRect = QRectF(
        viewportObj["x"].toDouble(),
        viewportObj["y"].toDouble(),
        viewportObj["width"].toDouble(),
        viewportObj["height"].toDouble()
    );
    
    return bookmark;
}

void EnhancedBookmark::generateId()
{
    id = QUuid::createUuid().toString(QUuid::WithoutBraces);
}

void EnhancedBookmark::updateAccess()
{
    lastAccessed = QDateTime::currentDateTime();
    accessCount++;
}

// BookmarkManager implementation
BookmarkManager::BookmarkManager(QObject *parent)
    : QObject(parent)
    , m_settings(new QSettings(this))
    , m_autoSaveEnabled(true)
    , m_maxBookmarks(DEFAULT_MAX_BOOKMARKS)
{
    // Register the custom type
    qRegisterMetaType<EnhancedBookmark>("EnhancedBookmark");
    
    // Load existing bookmarks
    loadFromSettings();
}

BookmarkManager::~BookmarkManager()
{
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
}

QString BookmarkManager::addBookmark(const EnhancedBookmark& bookmark)
{
    if (!isValidBookmark(bookmark)) {
        qWarning() << "Invalid bookmark provided to addBookmark";
        return QString();
    }
    
    EnhancedBookmark newBookmark = bookmark;
    
    // Generate ID if not provided
    if (newBookmark.id.isEmpty()) {
        newBookmark.generateId();
    }
    
    // Ensure unique ID
    while (bookmarkExists(newBookmark.id)) {
        newBookmark.generateId();
    }
    
    // Set file name if not provided
    if (newBookmark.fileName.isEmpty()) {
        newBookmark.fileName = QFileInfo(newBookmark.filePath).fileName();
    }
    
    // Set creation time if not provided
    if (!newBookmark.created.isValid()) {
        newBookmark.created = QDateTime::currentDateTime();
    }
    
    // Set last accessed time
    newBookmark.lastAccessed = QDateTime::currentDateTime();
    
    // Add to list
    m_bookmarks.prepend(newBookmark);
    
    // Trim if necessary
    trimBookmarks();
    
    emit bookmarkAdded(newBookmark);
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
    
    return newBookmark.id;
}

bool BookmarkManager::updateBookmark(const QString& id, const EnhancedBookmark& bookmark)
{
    int index = findBookmarkIndex(id);
    if (index == -1) {
        return false;
    }
    
    if (!isValidBookmark(bookmark)) {
        return false;
    }
    
    EnhancedBookmark updatedBookmark = bookmark;
    updatedBookmark.id = id; // Preserve original ID
    
    m_bookmarks[index] = updatedBookmark;
    
    emit bookmarkUpdated(updatedBookmark);
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
    
    return true;
}

bool BookmarkManager::removeBookmark(const QString& id)
{
    int index = findBookmarkIndex(id);
    if (index == -1) {
        return false;
    }
    
    m_bookmarks.removeAt(index);
    
    emit bookmarkRemoved(id);
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
    
    return true;
}

bool BookmarkManager::removeBookmarksForDocument(const QString& filePath)
{
    bool removed = false;
    
    auto it = std::remove_if(m_bookmarks.begin(), m_bookmarks.end(),
                            [&filePath, &removed](const EnhancedBookmark& bookmark) {
                                if (bookmark.filePath == filePath) {
                                    removed = true;
                                    return true;
                                }
                                return false;
                            });
    
    if (it != m_bookmarks.end()) {
        m_bookmarks.erase(it, m_bookmarks.end());
        
        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
    
    return removed;
}

void BookmarkManager::clearAllBookmarks()
{
    m_bookmarks.clear();
    emit bookmarksCleared();
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
}

EnhancedBookmark BookmarkManager::getBookmark(const QString& id) const
{
    int index = findBookmarkIndex(id);
    if (index != -1) {
        return m_bookmarks[index];
    }
    return EnhancedBookmark();
}

QList<EnhancedBookmark> BookmarkManager::getAllBookmarks() const
{
    return m_bookmarks;
}

QList<EnhancedBookmark> BookmarkManager::getBookmarksForDocument(const QString& filePath) const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.filePath == filePath) {
            result.append(bookmark);
        }
    }
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getFavoriteBookmarks() const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.isFavorite) {
            result.append(bookmark);
        }
    }
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getRecentBookmarks(int maxCount) const
{
    QList<EnhancedBookmark> sorted = sortBookmarks(m_bookmarks, SortOrder::LastAccessed, false);
    return sorted.mid(0, qMin(maxCount, sorted.size()));
}

QList<EnhancedBookmark> BookmarkManager::getMostAccessedBookmarks(int maxCount) const
{
    QList<EnhancedBookmark> sorted = sortBookmarks(m_bookmarks, SortOrder::AccessCount, false);
    return sorted.mid(0, qMin(maxCount, sorted.size()));
}

QList<EnhancedBookmark> BookmarkManager::searchBookmarks(const QString& query) const
{
    QList<EnhancedBookmark> result;
    QString lowerQuery = query.toLower();
    
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.name.toLower().contains(lowerQuery) ||
            bookmark.description.toLower().contains(lowerQuery) ||
            bookmark.fileName.toLower().contains(lowerQuery) ||
            bookmark.notes.toLower().contains(lowerQuery) ||
            bookmark.category.toLower().contains(lowerQuery) ||
            bookmark.tags.join(" ").toLower().contains(lowerQuery)) {
            result.append(bookmark);
        }
    }
    
    return result;
}

QList<EnhancedBookmark> BookmarkManager::filterBookmarks(FilterType filter, const QString& value) const
{
    QList<EnhancedBookmark> result;
    
    switch (filter) {
    case FilterType::All:
        return m_bookmarks;
        
    case FilterType::Favorites:
        return getFavoriteBookmarks();
        
    case FilterType::RecentlyAccessed:
        return getRecentBookmarks(20);
        
    case FilterType::MostAccessed:
        return getMostAccessedBookmarks(20);
        
    case FilterType::ByCategory:
        return getBookmarksByCategory(value);
        
    case FilterType::ByTag:
        return getBookmarksByTag(value);
        
    case FilterType::ByDocument:
        return getBookmarksForDocument(value);
    }
    
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getBookmarksByCategory(const QString& category) const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.category == category) {
            result.append(bookmark);
        }
    }
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getBookmarksByTag(const QString& tag) const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.tags.contains(tag)) {
            result.append(bookmark);
        }
    }
    return result;
}

QStringList BookmarkManager::getAllCategories() const
{
    QStringList categories;
    for (const auto& bookmark : m_bookmarks) {
        if (!bookmark.category.isEmpty() && !categories.contains(bookmark.category)) {
            categories.append(bookmark.category);
        }
    }
    categories.sort();
    return categories;
}

QStringList BookmarkManager::getAllTags() const
{
    QStringList tags;
    for (const auto& bookmark : m_bookmarks) {
        for (const QString& tag : bookmark.tags) {
            if (!tag.isEmpty() && !tags.contains(tag)) {
                tags.append(tag);
            }
        }
    }
    tags.sort();
    return tags;
}

QStringList BookmarkManager::getCategoriesForDocument(const QString& filePath) const
{
    QStringList categories;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.filePath == filePath && !bookmark.category.isEmpty() && !categories.contains(bookmark.category)) {
            categories.append(bookmark.category);
        }
    }
    categories.sort();
    return categories;
}

QStringList BookmarkManager::getTagsForDocument(const QString& filePath) const
{
    QStringList tags;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.filePath == filePath) {
            for (const QString& tag : bookmark.tags) {
                if (!tag.isEmpty() && !tags.contains(tag)) {
                    tags.append(tag);
                }
            }
        }
    }
    tags.sort();
    return tags;
}

int BookmarkManager::getBookmarkCount() const
{
    return m_bookmarks.size();
}

int BookmarkManager::getBookmarkCountForDocument(const QString& filePath) const
{
    int count = 0;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.filePath == filePath) {
            count++;
        }
    }
    return count;
}

QDateTime BookmarkManager::getLastBookmarkTime() const
{
    QDateTime latest;
    for (const auto& bookmark : m_bookmarks) {
        if (!latest.isValid() || bookmark.created > latest) {
            latest = bookmark.created;
        }
    }
    return latest;
}

QStringList BookmarkManager::getMostUsedCategories(int maxCount) const
{
    QMap<QString, int> categoryCount;
    for (const auto& bookmark : m_bookmarks) {
        if (!bookmark.category.isEmpty()) {
            categoryCount[bookmark.category]++;
        }
    }

    QList<QPair<int, QString>> sortedCategories;
    for (auto it = categoryCount.begin(); it != categoryCount.end(); ++it) {
        sortedCategories.append(qMakePair(it.value(), it.key()));
    }

    std::sort(sortedCategories.begin(), sortedCategories.end(),
              [](const QPair<int, QString>& a, const QPair<int, QString>& b) {
                  return a.first > b.first;
              });

    QStringList result;
    for (int i = 0; i < qMin(maxCount, sortedCategories.size()); ++i) {
        result.append(sortedCategories[i].second);
    }
    return result;
}

QStringList BookmarkManager::getMostUsedTags(int maxCount) const
{
    QMap<QString, int> tagCount;
    for (const auto& bookmark : m_bookmarks) {
        for (const QString& tag : bookmark.tags) {
            if (!tag.isEmpty()) {
                tagCount[tag]++;
            }
        }
    }

    QList<QPair<int, QString>> sortedTags;
    for (auto it = tagCount.begin(); it != tagCount.end(); ++it) {
        sortedTags.append(qMakePair(it.value(), it.key()));
    }

    std::sort(sortedTags.begin(), sortedTags.end(),
              [](const QPair<int, QString>& a, const QPair<int, QString>& b) {
                  return a.first > b.first;
              });

    QStringList result;
    for (int i = 0; i < qMin(maxCount, sortedTags.size()); ++i) {
        result.append(sortedTags[i].second);
    }
    return result;
}

bool BookmarkManager::toggleFavorite(const QString& id)
{
    int index = findBookmarkIndex(id);
    if (index != -1) {
        m_bookmarks[index].isFavorite = !m_bookmarks[index].isFavorite;
        emit bookmarkUpdated(m_bookmarks[index]);
        emit favoriteToggled(id, m_bookmarks[index].isFavorite);

        if (m_autoSaveEnabled) {
            saveToSettings();
        }
        return true;
    }
    return false;
}

bool BookmarkManager::setFavorite(const QString& id, bool favorite)
{
    int index = findBookmarkIndex(id);
    if (index != -1) {
        bool oldFavorite = m_bookmarks[index].isFavorite;
        m_bookmarks[index].isFavorite = favorite;
        emit bookmarkUpdated(m_bookmarks[index]);
        if (oldFavorite != favorite) {
            emit favoriteToggled(id, favorite);
        }

        if (m_autoSaveEnabled) {
            saveToSettings();
        }
        return true;
    }
    return false;
}

void BookmarkManager::recordAccess(const QString& id)
{
    int index = findBookmarkIndex(id);
    if (index != -1) {
        m_bookmarks[index].lastAccessed = QDateTime::currentDateTime();
        m_bookmarks[index].accessCount++;
        emit bookmarkUpdated(m_bookmarks[index]);
        emit bookmarkAccessed(id);

        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
}

bool BookmarkManager::hasBookmarkForPage(const QString& filePath, int pageNumber) const
{
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.filePath == filePath && bookmark.pageNumber == pageNumber) {
            return true;
        }
    }
    return false;
}

void BookmarkManager::setAutoSaveEnabled(bool enabled)
{
    m_autoSaveEnabled = enabled;
}

bool BookmarkManager::isAutoSaveEnabled() const
{
    return m_autoSaveEnabled;
}

void BookmarkManager::setMaxBookmarks(int maxBookmarks)
{
    m_maxBookmarks = maxBookmarks;
    trimBookmarks();
}

int BookmarkManager::getMaxBookmarks() const
{
    return m_maxBookmarks;
}

bool BookmarkManager::exportBookmarks(const QString& filePath) const
{
    QJsonArray bookmarksArray;
    for (const auto& bookmark : m_bookmarks) {
        bookmarksArray.append(bookmark.toJson());
    }

    QJsonObject rootObject;
    rootObject["bookmarks"] = bookmarksArray;
    rootObject["version"] = "1.0";
    rootObject["exportDate"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(rootObject);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    file.write(doc.toJson());
    return true;
}

bool BookmarkManager::importBookmarks(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);

    if (!doc.isObject()) {
        return false;
    }

    QJsonObject rootObject = doc.object();
    QJsonArray bookmarksArray = rootObject["bookmarks"].toArray();

    for (const auto& value : bookmarksArray) {
        if (value.isObject()) {
            EnhancedBookmark bookmark = EnhancedBookmark::fromJson(value.toObject());

            // Generate new ID to avoid conflicts
            bookmark.id = QUuid::createUuid().toString(QUuid::WithoutBraces);

            if (isValidBookmark(bookmark)) {
                m_bookmarks.append(bookmark);
                emit bookmarkAdded(bookmark);
            }
        }
    }

    if (m_autoSaveEnabled) {
        saveToSettings();
    }

    return true;
}

bool BookmarkManager::exportBookmarksForDocument(const QString& documentPath, const QString& exportPath) const
{
    QList<EnhancedBookmark> documentBookmarks = getBookmarksForDocument(documentPath);

    QJsonArray bookmarksArray;
    for (const auto& bookmark : documentBookmarks) {
        bookmarksArray.append(bookmark.toJson());
    }

    QJsonObject rootObject;
    rootObject["bookmarks"] = bookmarksArray;
    rootObject["version"] = "1.0";
    rootObject["exportDate"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    rootObject["documentPath"] = documentPath;

    QJsonDocument doc(rootObject);

    QFile file(exportPath);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    file.write(doc.toJson());
    return true;
}

void BookmarkManager::removeOrphanedBookmarks()
{
    auto it = std::remove_if(m_bookmarks.begin(), m_bookmarks.end(),
                            [](const EnhancedBookmark& bookmark) {
                                QFileInfo fileInfo(bookmark.filePath);
                                return !fileInfo.exists();
                            });

    if (it != m_bookmarks.end()) {
        m_bookmarks.erase(it, m_bookmarks.end());
        emit bookmarksCleared(); // Signal that bookmarks were removed

        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
}

void BookmarkManager::removeOldBookmarks(int daysOld)
{
    QDateTime cutoffDate = QDateTime::currentDateTime().addDays(-daysOld);

    auto it = std::remove_if(m_bookmarks.begin(), m_bookmarks.end(),
                            [cutoffDate](const EnhancedBookmark& bookmark) {
                                return bookmark.created < cutoffDate;
                            });

    if (it != m_bookmarks.end()) {
        m_bookmarks.erase(it, m_bookmarks.end());
        emit bookmarksCleared(); // Signal that bookmarks were removed

        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
}

// Missing method implementations

void BookmarkManager::saveToSettings()
{
    if (!m_settings) return;

    m_settings->beginGroup(SETTINGS_GROUP);

    QJsonArray bookmarksArray;
    for (const auto& bookmark : m_bookmarks) {
        bookmarksArray.append(bookmark.toJson());
    }

    QJsonDocument doc(bookmarksArray);
    m_settings->setValue(BOOKMARKS_KEY, doc.toJson(QJsonDocument::Compact));
    m_settings->setValue("maxBookmarks", m_maxBookmarks);
    m_settings->setValue("autoSaveEnabled", m_autoSaveEnabled);

    m_settings->endGroup();
}

void BookmarkManager::loadFromSettings()
{
    if (!m_settings) return;

    m_settings->beginGroup(SETTINGS_GROUP);

    // Load configuration
    m_maxBookmarks = m_settings->value("maxBookmarks", DEFAULT_MAX_BOOKMARKS).toInt();
    m_autoSaveEnabled = m_settings->value("autoSaveEnabled", true).toBool();

    // Load bookmarks
    QByteArray bookmarksData = m_settings->value(BOOKMARKS_KEY).toByteArray();
    if (!bookmarksData.isEmpty()) {
        QJsonDocument doc = QJsonDocument::fromJson(bookmarksData);
        if (doc.isArray()) {
            QJsonArray bookmarksArray = doc.array();
            m_bookmarks.clear();

            for (const auto& value : bookmarksArray) {
                if (value.isObject()) {
                    EnhancedBookmark bookmark = EnhancedBookmark::fromJson(value.toObject());
                    if (!bookmark.id.isEmpty()) {
                        m_bookmarks.append(bookmark);
                    }
                }
            }
        }
    }

    m_settings->endGroup();
}

bool BookmarkManager::bookmarkExists(const QString& id) const
{
    return findBookmarkIndex(id) != -1;
}

void BookmarkManager::trimBookmarks()
{
    if (m_bookmarks.size() > m_maxBookmarks) {
        // Keep the most recent bookmarks (they are at the beginning of the list)
        m_bookmarks = m_bookmarks.mid(0, m_maxBookmarks);
    }
}

int BookmarkManager::findBookmarkIndex(const QString& id) const
{
    for (int i = 0; i < m_bookmarks.size(); ++i) {
        if (m_bookmarks[i].id == id) {
            return i;
        }
    }
    return -1;
}

bool BookmarkManager::isValidBookmark(const EnhancedBookmark& bookmark) const
{
    // Basic validation
    if (bookmark.filePath.isEmpty()) {
        return false;
    }

    if (bookmark.name.isEmpty()) {
        return false;
    }

    if (bookmark.pageNumber < 0) {
        return false;
    }

    if (bookmark.zoomFactor <= 0.0) {
        return false;
    }

    return true;
}

QList<EnhancedBookmark> BookmarkManager::sortBookmarks(const QList<EnhancedBookmark>& bookmarks, SortOrder order, bool ascending) const
{
    QList<EnhancedBookmark> sorted = bookmarks;

    switch (order) {
    case SortOrder::Name:
        std::sort(sorted.begin(), sorted.end(), [ascending](const EnhancedBookmark& a, const EnhancedBookmark& b) {
            return ascending ? a.name < b.name : a.name > b.name;
        });
        break;

    case SortOrder::Created:
        std::sort(sorted.begin(), sorted.end(), [ascending](const EnhancedBookmark& a, const EnhancedBookmark& b) {
            return ascending ? a.created < b.created : a.created > b.created;
        });
        break;

    case SortOrder::LastAccessed:
        std::sort(sorted.begin(), sorted.end(), [ascending](const EnhancedBookmark& a, const EnhancedBookmark& b) {
            return ascending ? a.lastAccessed < b.lastAccessed : a.lastAccessed > b.lastAccessed;
        });
        break;

    case SortOrder::AccessCount:
        std::sort(sorted.begin(), sorted.end(), [ascending](const EnhancedBookmark& a, const EnhancedBookmark& b) {
            return ascending ? a.accessCount < b.accessCount : a.accessCount > b.accessCount;
        });
        break;

    case SortOrder::PageNumber:
        std::sort(sorted.begin(), sorted.end(), [ascending](const EnhancedBookmark& a, const EnhancedBookmark& b) {
            return ascending ? a.pageNumber < b.pageNumber : a.pageNumber > b.pageNumber;
        });
        break;

    case SortOrder::Category:
        std::sort(sorted.begin(), sorted.end(), [ascending](const EnhancedBookmark& a, const EnhancedBookmark& b) {
            return ascending ? a.category < b.category : a.category > b.category;
        });
        break;
    }

    return sorted;
}
